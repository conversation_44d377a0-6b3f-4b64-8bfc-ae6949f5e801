import { CACHE_APIS } from '../api-base-cache-keys';
import { ApiR<PERSON>ponse, BestRecommenedStitcherResponse, ChatRequestsItem, MutationApiConfig, PaginatedApiConfig, PaginatedResponse, QueryApiConfig } from '../types';

const C = CACHE_APIS.CHAT;

export namespace ChatApis {
  export const bestStitchers = 'bestStitchers' as const;
  export const chatRequestUser = 'chatRequestUser' as const;
  export const chatPendingRequestsList = 'chatPendingRequestsList' as const;
  export const chatAcceptPendingRequest = 'chatAcceptPendingRequest' as const;
}

export const chatApiConfig = {
  bestStitchers: {
    path: '/chat/recommended-stichers',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<BestRecommenedStitcherResponse>,
    baseCacheKey: C.best_stichers,
    staleTime: 15 * 60 * 1000, // 15 minutes
    type: 'query' as const,
  } satisfies QueryApiConfig<BestRecommenedStitcherResponse>,
  chatRequestUser: {
    path: '/chat/requests',
    baseCacheKey: C.request_user_for_chat,
    staleTime: 0,
    method: 'POST',
    protected: true,
    type: 'mutation' as const,
    mutationBody: undefined as unknown as any,
    responseType: undefined as unknown as ApiResponse<any>,
  } satisfies MutationApiConfig<any, any>,
  chatPendingRequestsList: {
    path: '/chat/requests/list',
    protected: true,
    baseCacheKey: C.pending_requests_list,
    responseType: undefined as unknown as PaginatedResponse<ChatRequestsItem[]>,
    method: 'GET',
    pageSize: 30,
    staleTime: 15 * 60 * 1000, // 15 minutes
    type: 'paginated' as const,
    isPaginated: true,
  } satisfies PaginatedApiConfig<ChatRequestsItem[]>,
  chatAcceptPendingRequest: {
    path: '/chat/requests/accept',
    baseCacheKey: C.accept_pending_request,
    staleTime: 0,
    method: 'POST',
    protected: true,
    type: 'mutation' as const,
    mutationBody: undefined as unknown as any,
    responseType: undefined as unknown as ApiResponse<any>,
  } satisfies MutationApiConfig<any, any>,
};
