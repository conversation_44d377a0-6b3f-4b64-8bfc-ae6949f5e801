import { RefreshControl } from 'react-native';
import Animated from 'react-native-reanimated';

interface PullToRefreshWrapperProps {
  children: React.ReactNode;
  onRefresh: () => void;
  isRefetching: boolean;
  threshold?: number;
}

export const PullToRefreshWrapper: React.FC<PullToRefreshWrapperProps> = ({ children, isRefetching, onRefresh, threshold }) => {
  return (
    <Animated.ScrollView
      onScroll={event => {
        const offsetY = event.nativeEvent.contentOffset.y;
        if (offsetY < (threshold ?? -100) && !isRefetching) {
          onRefresh();
        }
      }}
      scrollEventThrottle={16}
      contentContainerStyle={{ flexGrow: 1 }}
      refreshControl={
        <RefreshControl
          refreshing={isRefetching}
          onRefresh={onRefresh}
          tintColor="#000" // Customize the color of the spinner
          title="Pull to refresh"
          titleColor="#000" // Customize the title color
          colors={['#000']} // Customize the spinner color
          progressBackgroundColor="#fff" // Customize the background color of the spinner
        />
      }>
      {children}
    </Animated.ScrollView>
  );
};
