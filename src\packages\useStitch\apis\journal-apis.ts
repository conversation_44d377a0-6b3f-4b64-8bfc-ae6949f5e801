// packages/useStitch/apis/profile-apis.ts
import { CACHE_APIS } from '../api-base-cache-keys';
import { CreateJournalCoverMutationBody, JournalCategoryCoversResponse, JournalCategoryListResponse, JournalDetailsResponse, JournalFeedItem, JournalSearchHistoryItem } from '../types';
import { ApiResponse, MutationApiConfig, PaginatedApiConfig, PaginatedResponse, QueryApiConfig } from '../types/common-api-types';

const C = CACHE_APIS.JOURNAL;

export namespace JournalApis {
  export const journalFeedListByCategory = 'journalFeedListByCategory' as const;
  export const journalDetailsById = 'journalDetailsById' as const;
  export const journalSearchHistoryList = 'journalSearchHistoryList' as const;
  export const createJournal = 'createJournal' as const;
  export const createJournalCategory = 'createJournalCategory' as const;
  export const editJournal = 'editJournal' as const;
  export const deleteJournal = 'deleteJournal' as const;
  export const journalCategoryList = 'journalCategoryList' as const;
  export const journalCategoryCovers = 'journalCategoryCovers' as const;
}

export const journalApiConfig = {
  createJournal: {
    path: '/journal/add',
    method: 'POST',
    protected: true,
    mutationBody: undefined as unknown as any,
    responseType: undefined as unknown as ApiResponse<any>,
    baseCacheKey: C.add_journal,
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<any, any>,
  editJournal: {
    path: '/journal/update',
    method: 'POST',
    protected: true,
    mutationBody: undefined as unknown as any,
    responseType: undefined as unknown as ApiResponse<any>,
    baseCacheKey: C.edit_journal,
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<any, any>,
  deleteJournal: {
    path: '/journal/delete',
    method: 'POST',
    protected: true,
    mutationBody: undefined as unknown as any,
    responseType: undefined as unknown as ApiResponse<any>,
    baseCacheKey: C.delete_journal,
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<any, any>,
  journalDetailsById: {
    path: '/journal',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<JournalDetailsResponse>,
    baseCacheKey: C.journal_details,
    staleTime: 0,
    type: 'query' as const,
  } satisfies QueryApiConfig<JournalDetailsResponse>,
  journalSearchHistoryList: {
    path: '/journal/search-history',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<JournalSearchHistoryItem[]>,
    baseCacheKey: C.journal_search_history,
    staleTime: 15 * 60 * 1000, // 15 mins
    type: 'query' as const,
  } satisfies QueryApiConfig<JournalSearchHistoryItem[]>,
  journalCategoryList: {
    path: '/journal/category/list',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<JournalCategoryListResponse>,
    baseCacheKey: C.journal_category_list,
    staleTime: 0,
    type: 'query' as const,
  } satisfies QueryApiConfig<JournalCategoryListResponse>,
  journalCategoryCovers: {
    path: '/journal/category/covers',
    method: 'GET',
    protected: true,
    responseType: undefined as unknown as ApiResponse<JournalCategoryCoversResponse>,
    baseCacheKey: C.journal_category_covers,
    staleTime: 0,
    type: 'query' as const,
  } satisfies QueryApiConfig<JournalCategoryCoversResponse>,
  createJournalCategory: {
    path: '/journal/category/add',
    method: 'POST',
    protected: true,
    mutationBody: undefined as unknown as CreateJournalCoverMutationBody,
    responseType: undefined as unknown as ApiResponse<any>,
    baseCacheKey: C.create_journal_category,
    staleTime: 0,
    type: 'mutation' as const,
  } satisfies MutationApiConfig<any, CreateJournalCoverMutationBody>,
  journalFeedListByCategory: {
    path: '/journal/list/',
    protected: true,
    baseCacheKey: C.journal_feed_list,
    isPaginated: true,
    responseType: undefined as unknown as PaginatedResponse<JournalFeedItem[]>,
    method: 'GET',
    pageSize: 20,
    staleTime: 15 * 60 * 1000, // 15 minutes
    type: 'paginated' as const,
  } satisfies PaginatedApiConfig<JournalFeedItem[]>,
};
