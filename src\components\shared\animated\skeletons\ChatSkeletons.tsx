import { View } from '@components/native';
import { Dimensions, FlatList } from 'react-native';
import { Skeleton } from './Skeleton';

const { width } = Dimensions.get('screen');

export const BestStichersHorizontalSkeleton = () => {
  return (
    <FlatList
      horizontal={true}
      scrollEnabled={false}
      showsHorizontalScrollIndicator={false}
      pagingEnabled
      contentContainerStyle={{ paddingHorizontal: 20, marginBottom: 20, paddingEnd: width / 3 }}
      ItemSeparatorComponent={() => <View w={12} />}
      data={Array.from({ length: 10 })}
      renderItem={({ item, index }) => {
        return <Skeleton h={130} w={120} br={14} />;
      }}
    />
  );
};
