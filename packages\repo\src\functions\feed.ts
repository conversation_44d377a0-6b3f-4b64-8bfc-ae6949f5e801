import {
  bookmarksTable,
  commentsTable,
  feedItemTable,
  likesTable,
  pollOptionsTable,
  pollsTable,
  pollVotesTable,
  postsTable,
  userDetails
} from "@repo/db/schema";
import { RepoPayload } from "./types.js";
import { and, count, eq, exists, isNull, not, sql } from "drizzle-orm";
import db from "@repo/db";
import { Paginator } from "@repo/lib";

type CreateFeedItemPayload =
  | {
      type: "post";
      title: string;
      body: string;
      userId: string;
      disableComments?: boolean;
    }
  | {
      type: "poll";
      title: string;
      options: string[];
      userId: string;
      disableComments?: boolean;
    };

export class FeedRepo {
  static async createFeedItem(payload: RepoPayload<CreateFeedItemPayload>) {
    // TODO: create a feed item
    const [feedItem] = await payload.tx
      .insert(feedItemTable)
      .values({
        type: payload.data.type,
        userId: payload.data.userId,
        disableComments: payload.data.disableComments
      })
      .returning();
    if (!feedItem) throw new Error("Failed to create feed item");
    // TODO: create a post or poll depending on type
    switch (payload.data.type) {
      case "post":
        {
          await payload.tx.insert(postsTable).values({
            id: feedItem.id,
            title: payload.data.title,
            body: payload.data.body
          });
        }
        break;
      case "poll":
        {
          const [poll] = await payload.tx
            .insert(pollsTable)
            .values({
              id: feedItem.id,
              title: payload.data.title
            })
            .returning();
          if (!poll) throw new Error("Failed to create poll");
          await payload.tx.insert(pollOptionsTable).values(
            payload.data.options.map((option, i) => ({
              title: option,
              order: i,
              pollId: poll.id
            }))
          );
        }
        break;
    }
  }
  static async likeFeedItem(payload: RepoPayload<{ userId: string; feedItemId: string }>) {
    const [like] = await payload.tx
      .select()
      .from(likesTable)
      .where(
        and(
          eq(likesTable.userId, payload.data.userId),
          eq(likesTable.feedItemId, payload.data.feedItemId)
        )
      );
    if (!like) {
      await payload.tx.insert(likesTable).values({
        userId: payload.data.userId,
        feedItemId: payload.data.feedItemId
      });
      return true;
    } else if (like && like.deletedAt) {
      await payload.tx
        .update(likesTable)
        .set({ deletedAt: null })
        .where(eq(likesTable.id, like.id));
      return true;
    } else {
      await payload.tx
        .update(likesTable)
        .set({ deletedAt: new Date() })
        .where(eq(likesTable.id, like.id));
      return false;
    }
  }
  static async bookmarkFeedItem(payload: RepoPayload<{ userId: string; feedItemId: string }>) {
    const [bookmark] = await payload.tx
      .select()
      .from(bookmarksTable)
      .where(
        and(
          eq(bookmarksTable.userId, payload.data.userId),
          eq(bookmarksTable.feedItemId, payload.data.feedItemId)
        )
      );
    if (!bookmark) {
      await payload.tx.insert(bookmarksTable).values({
        userId: payload.data.userId,
        feedItemId: payload.data.feedItemId
      });
      return true;
    } else if (bookmark && bookmark.deletedAt) {
      await payload.tx
        .update(bookmarksTable)
        .set({ deletedAt: null })
        .where(eq(bookmarksTable.id, bookmark.id));
      return true;
    } else {
      await payload.tx
        .update(bookmarksTable)
        .set({ deletedAt: new Date() })
        .where(eq(bookmarksTable.id, bookmark.id));
      return false;
    }
  }
  static async selectPollOption(
    payload: RepoPayload<{ userId: string; pollOptionId: string; pollId: string }>
  ) {
    const [vote] = await payload.tx
      .select()
      .from(pollVotesTable)
      .where(
        and(
          eq(pollVotesTable.userId, payload.data.userId),
          eq(pollVotesTable.pollOptionId, payload.data.pollOptionId),
          eq(pollVotesTable.pollId, payload.data.pollId)
        )
      );
    await payload.tx
      .update(pollVotesTable)
      .set({
        deletedAt: new Date()
      })
      .where(
        and(
          eq(pollVotesTable.pollId, payload.data.pollId),
          isNull(pollVotesTable.deletedAt),
          not(eq(pollVotesTable.pollOptionId, payload.data.pollOptionId)),
          eq(pollVotesTable.userId, payload.data.userId)
        )
      );
    if (!vote) {
      await payload.tx.insert(pollVotesTable).values({
        userId: payload.data.userId,
        pollOptionId: payload.data.pollOptionId,
        pollId: payload.data.pollId
      });
      return true;
    } else if (vote && vote.deletedAt) {
      await payload.tx
        .update(pollVotesTable)
        .set({ deletedAt: null })
        .where(eq(pollVotesTable.id, vote.id));
      return true;
    } else {
      await payload.tx
        .update(pollVotesTable)
        .set({ deletedAt: new Date() })
        .where(eq(pollVotesTable.id, vote.id));
      return false;
    }
  }
  static async deleteFeedItem(payload: RepoPayload<{ userId: string; feedItemId: string }>) {
    await payload.tx
      .update(feedItemTable)
      .set({ deletedAt: new Date() })
      .where(eq(feedItemTable.id, payload.data.feedItemId));
    return true;
  }
  static async updateFeedItem(
    payload: RepoPayload<{
      userId: string;
      feedItemId: string;
      title?: string;
      description?: string;
    }>
  ) {
    const [feedItem] = await payload.tx
      .select()
      .from(feedItemTable)
      .where(
        and(
          eq(feedItemTable.id, payload.data.feedItemId),
          eq(feedItemTable.userId, payload.data.userId),
          isNull(feedItemTable.deletedAt),
          eq(feedItemTable.type, "post")
        )
      );
    if (!feedItem) return false;
    if (payload.data.title || payload.data.description) {
      await payload.tx
        .update(postsTable)
        .set({ title: payload.data.title, body: payload.data.description! })
        .where(eq(feedItemTable.id, payload.data.feedItemId));
    }
    return true;
  }

  static async listFeedItems(query: Record<string, string>) {
    const { limit, offset } = Paginator.getPage(query);

    const total = await Paginator.getTotalCount(
      db.select({ count: count() }).from(feedItemTable).where(isNull(feedItemTable.deletedAt))
    );

    const rows = await db
      .select({
        user: {
          id: userDetails.userId,
          avatar: userDetails.avatarUrl,
          username: userDetails.userName
        },
        id: feedItemTable.id,
        type: feedItemTable.type,
        title: sql<string>`CASE
        WHEN ${feedItemTable.type} = 'post' THEN ${postsTable.title}
        WHEN ${feedItemTable.type} = 'poll' THEN ${pollsTable.title}
      END
    `.as("title"),
        body: sql<string | null>`
      CASE
        WHEN ${feedItemTable.type} = 'post' THEN ${postsTable.body}
        ELSE NULL
      END
    `.as("body"),
        poll: sql<{ option: string; count: number }[]>`
          CASE
            WHEN ${feedItemTable.type} = 'poll'
            THEN JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', ${pollOptionsTable.id},
                'option', ${pollOptionsTable.title},
                'votes', ${db.$count(pollVotesTable, and(eq(pollVotesTable.pollOptionId, pollOptionsTable.id), isNull(pollVotesTable.deletedAt)))},
                'total', ${db.$count(pollVotesTable, and(eq(pollVotesTable.pollId, feedItemTable.id), isNull(pollVotesTable.deletedAt)))}
              )
              ORDER BY ${pollOptionsTable.order}
              )
          END
        `.as("poll_options"),

        // poll: db
        //   .selectDistinctOn([pollOptionsTable.pollId], {
        //     id: pollOptionsTable.id,
        //     option: pollOptionsTable.title,
        //     count: db.$count(
        //       pollVotesTable,
        //       and(
        //         eq(pollVotesTable.pollOptionId, pollOptionsTable.id),
        //         isNull(pollVotesTable.deletedAt)
        //       )
        //     )
        //   })
        //   .from(pollOptionsTable)
        //   .where(eq(pollOptionsTable.pollId, feedItemTable.id))
        //   .orderBy(pollOptionsTable.order)
        //   .as("poll_options"),
        disableComments: feedItemTable.disableComments,

        liked: exists(
          db
            .select()
            .from(likesTable)
            .where(
              and(
                eq(likesTable.feedItemId, feedItemTable.id),
                isNull(likesTable.deletedAt),
                eq(likesTable.userId, feedItemTable.userId)
              )
            )
        ),
        bookmarked: exists(
          db
            .select()
            .from(bookmarksTable)
            .where(
              and(
                eq(bookmarksTable.feedItemId, feedItemTable.id),
                isNull(bookmarksTable.deletedAt),
                eq(bookmarksTable.userId, feedItemTable.userId)
              )
            )
        ),

        metrics: {
          totalLikes: db
            .$count(
              likesTable,
              and(eq(likesTable.feedItemId, feedItemTable.id), isNull(likesTable.deletedAt))
            )
            .as("totalLikes"),
          totalComments: db
            .$count(
              commentsTable,
              and(eq(commentsTable.feedItemId, feedItemTable.id), isNull(commentsTable.deletedAt))
            )
            .as("totalComments")
        }
      })
      .from(feedItemTable)
      .leftJoin(postsTable, eq(feedItemTable.id, postsTable.id))
      .leftJoin(pollsTable, eq(feedItemTable.id, pollsTable.id))
      .leftJoin(pollOptionsTable, eq(pollsTable.id, pollOptionsTable.pollId))
      .leftJoin(userDetails, eq(userDetails.userId, feedItemTable.userId))
      .where(isNull(feedItemTable.deletedAt))
      .groupBy(
        userDetails.id,
        feedItemTable.id,
        feedItemTable.type,
        postsTable.title,
        postsTable.body,
        pollsTable.title,
        feedItemTable.disableComments,
        pollOptionsTable.pollId
      )
      .limit(limit)
      .offset(offset);

    return Paginator.paginate(query, rows, total);
  }
}
