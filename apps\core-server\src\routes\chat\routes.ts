import { Router } from "express";
import { <PERSON><PERSON><PERSON><PERSON>roll<PERSON> } from "./controller.js";
import { AuthMiddlewares } from "@/middlewares/auth-middleware.js";
import { createChatRequestsRouter } from "./requests/router.js";

export function createChatRouter() {
  const router = Router();
  router.get("/recommended-stichers", ChatController.getBestStichers);
  router.get("/conversations-list", ChatController.getConversationsList);
  router.get(
    "/conversation-id",
    AuthMiddlewares.validateAccessToken,
    ChatController.getConversationId,
  );
  router.use("/requests", createChatRequestsRouter());
  return router;
}
