import { commentsTable, feedItemTable, userDetails } from "@repo/db/schema";
import { RepoPayload } from "./types.js";
import { and, count, desc, eq, isNull } from "drizzle-orm";
import { Paginator } from "@repo/lib";

export class CommentsRepo {
  static async createComment(
    payload: RepoPayload<{
      userId: string;
      feedItemId: string;
      text: string;
      replyTo?: string;
      threadId?: string;
    }>
  ) {
    const [feedItem] = await payload.tx
      .select()
      .from(feedItemTable)
      .where(and(eq(feedItemTable.id, payload.data.feedItemId), isNull(feedItemTable.deletedAt)));
    if (!feedItem) throw new Error("Feed item not found");
    if (feedItem.disableComments) throw new Error("Comments are disabled for this feed item");

    const [comment] = await payload.tx
      .insert(commentsTable)
      .values({
        userId: payload.data.userId,
        feedItemId: payload.data.feedItemId,
        text: payload.data.text,
        replyTo: payload.data.replyTo || payload.data.threadId,
        rootCommentId: payload.data.threadId
      })
      .returning();
    if (!comment) throw new Error("Failed to create comment");
    return comment;
  }
  static async getComments(
    payload: RepoPayload<{ feedItemId: string; threadId?: string; limit?: string; page?: string }>
  ) {
    const { limit, offset } = Paginator.getPage(payload.data);

    const filters = [
      eq(commentsTable.feedItemId, payload.data.feedItemId),
      isNull(commentsTable.deletedAt)
    ];
    if (payload.data.threadId) {
      filters.push(eq(commentsTable.rootCommentId, payload.data.threadId));
    }
    const total = await Paginator.getTotalCount(
      payload.tx
        .select({ count: count() })
        .from(commentsTable)
        .where(and(...filters))
    );

    const rows = await payload.tx
      .select({
        user: {
          id: userDetails.id,
          username: userDetails.userName,
          avatar: userDetails.avatarUrl
        },
        id: commentsTable.id,
        replyTo: commentsTable.replyTo,
        text: commentsTable.text,
        createdAt: commentsTable.createdAt,
        editedAt: commentsTable.editedAt
      })
      .from(commentsTable)
      .innerJoin(userDetails, eq(userDetails.userId, commentsTable.userId))
      .where(and(...filters))
      .orderBy(desc(commentsTable.updatedAt))
      .groupBy(commentsTable.id, userDetails.id)
      .limit(limit)
      .offset(offset);
    return Paginator.paginate(payload.data, rows, total);
  }
  static async deleteComment(payload: RepoPayload<{ commentId: string; userId: string }>) {
    const [comment] = await payload.tx
      .update(commentsTable)
      .set({ deletedAt: new Date() })
      .where(
        and(
          eq(commentsTable.id, payload.data.commentId),
          eq(commentsTable.userId, payload.data.userId)
        )
      )
      .returning();
    if (!comment) throw new Error("Comment not found");
    return true;
  }
  static async editComment(
    payload: RepoPayload<{ commentId: string; text: string; userId: string }>
  ) {
    const [comment] = await payload.tx
      .update(commentsTable)
      .set({ text: payload.data.text, editedAt: new Date() })
      .where(
        and(
          eq(commentsTable.id, payload.data.commentId),
          eq(commentsTable.userId, payload.data.userId)
        )
      )
      .returning();
    if (!comment) throw new Error("Comment not found");
    return true;
  }
}
