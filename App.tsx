import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { Platform, StatusBar } from 'react-native';
import 'react-native-gesture-handler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import 'react-native-reanimated';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { enableScreens } from 'react-native-screens';
import Reactotron from 'reactotron-react-native';
import { AuthProvider, NotifierProvider, SocketConnnectionProvider, ThemeProvider } from './src/context';
import FirebaseProvider from './src/context/Firebase/FirebaseContext';
import { NetworkProvider } from './src/hooks/useNetwork';
import StitchApp from './src/navigation/StitchApp';
import './src/packages/db/init-db.native';

if (__DEV__) {
  Reactotron.useReactNative({
    networking: {
      ignoreUrls: /symbolicate/, // Optional: Ignore specific URLs
    },
  })
    .configure({ host: 'localhost' })
    .connect();
}
enableScreens(); // Better memory usage
const queryClient = new QueryClient();

function composeProviders(...providers: React.JSXElementConstructor<{ children: React.ReactNode }>[]) {
  return providers.reduce(
    (AccumulatedProviders, CurrentProvider) =>
      ({ children }) =>
        (
          <AccumulatedProviders>
            <CurrentProvider>{children}</CurrentProvider>
          </AccumulatedProviders>
        ),
    ({ children }) => <>{children}</>,
  );
}

const AppProviderTree = composeProviders(
  props => <FirebaseProvider {...props} disableFirebase={Platform.OS == 'ios'} />,
  NetworkProvider,
  SafeAreaProvider,
  ThemeProvider,
  props => <QueryClientProvider client={queryClient} {...props} />,
  GestureHandlerRootView,
  BottomSheetModalProvider,
  AuthProvider,
  SocketConnnectionProvider,
  NotifierProvider,
);

export default function App() {
  return (
    <AppProviderTree>
      <StatusBar barStyle={'dark-content'} />
      <StitchApp />
    </AppProviderTree>
  );
}
