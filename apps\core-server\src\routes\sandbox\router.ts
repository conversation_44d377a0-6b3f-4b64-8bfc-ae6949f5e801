import { Request, Response, Router } from "express";
import { SandboxController } from "./controller.js";
import { PushNotificationManager } from "@repo/lib";

export function createSandboxRouter() {
  const router = Router();
  router.get("/get-questions", SandboxController.getOnboardQuestions);
  router.post("/notify", async (req: Request, res: Response) => {
    try {
      const { body, title, data, fcmToken } = req.body;
      PushNotificationManager.sendNotification([
        {
          notification: {
            body: body,
            title: title,
          },
          token: fcmToken,
          data: data,
        },
      ]);
      res.status(200).json({
        success: true,
        message: "Notification Send",
      });
    } catch (error) {
      res.status(400).json({
        success: true,
        message: "Something bad happened",
        error: error,
      });
    }
  });
  return router;
}

// /api/onboarding/submit-questions
// /api/onboarding/
