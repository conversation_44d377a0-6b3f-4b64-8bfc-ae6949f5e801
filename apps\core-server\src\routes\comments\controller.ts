import type { Request, Response, NextFunction } from "express";
import {
  createCommentValidator,
  editCommentValidator,
  getCommentsValidator,
} from "./validator.js";
import { bodyValidator, queryValidator } from "@/lib/validation-module.js";
import { Infer } from "@vinejs/vine/types";
import db from "@repo/db";
import { commentsTable, userDetails } from "@repo/db/schema";
import { and, count, desc, eq, isNull } from "drizzle-orm";
import { Paginator } from "@/lib/pagination-module.js";
import { CommentsRepo } from "@repo/repo";
export class CommentsController {
  @bodyValidator(createCommentValidator)
  static async createComment(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof createCommentValidator>;
      await db.transaction(async (tx) => {
        await CommentsRepo.createComment({
          data: {
            feedItemId: payload.feedItemId,
            text: payload.text,
            userId: req.user.id,
            replyTo: payload.replyTo,
            threadId: payload.threadId,
          },
          tx,
        });
      });

      res.json({ message: "Comment created", success: true });
    } catch (error) {
      next(error);
    }
  }

  static async deleteComment(req: Request, res: Response, next: NextFunction) {
    try {
      const commentId = req.params.id as string;
      await db.transaction(async (tx) => {
        await CommentsRepo.deleteComment({
          data: {
            commentId,
            userId: req.user.id,
          },
          tx,
        });
      });
      res.json({ message: "Comment deleted successfully" });
    } catch (error) {
      next(error);
    }
  }
  @queryValidator(getCommentsValidator)
  static async getComments(req: Request, res: Response, next: NextFunction) {
    try {
      const query = req.query as Infer<typeof getCommentsValidator>;
      const data = await CommentsRepo.getComments({
        tx: db,
        data: {
          feedItemId: query.feedItemId,
          limit: query.limit,
          page: query.page,
          threadId: query.threadId,
        },
      });
      res.json({ data });
    } catch (error) {
      next(error);
    }
  }

  @bodyValidator(editCommentValidator)
  static async editComment(req: Request, res: Response, next: NextFunction) {
    try {
      const commentId = req.params.id as string;
      const payload = req.body as Infer<typeof editCommentValidator>;
      await db.transaction(async (tx) => {
        await CommentsRepo.editComment({
          data: {
            commentId,
            text: payload.text,
            userId: req.user.id,
          },
          tx,
        });
      });
      res.json({ message: "Comment edited successfully" });
    } catch (error) {
      next(error);
    }
  }
}
