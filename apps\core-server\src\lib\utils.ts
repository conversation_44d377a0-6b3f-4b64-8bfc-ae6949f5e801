export class UTILS {
  static getRoomId(user1: string, user2: string) {
    if (
      !user1 ||
      !user2 ||
      typeof user1 !== "string" ||
      typeof user2 !== "string"
    ) {
      throw new Error("Both user1 and user2 must be non-empty strings");
    }

    const sortedIds = [user1, user2].sort();

    return {
      roomId: `${sortedIds[0]}__${sortedIds[1]}`,
      sortedUser1: sortedIds[0],
      sortedUser2: sortedIds[0],
    };
  }
}
