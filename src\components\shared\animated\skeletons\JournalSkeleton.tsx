import { View } from '@components/native';
import { Dimensions } from 'react-native';
import { Skeleton } from './Skeleton';
const { width } = Dimensions.get('screen');

export const JournalFeedSkeleton = () => (
  <View flex={1} bg="background">
    <View p={20}>
      <Skeleton h={40} br={12} />
    </View>
    <View h={200}>
      <View display="flex" ai="center" fd="row" jc="center">
        <Skeleton left={-40} btr={20} bbr={20} h={width / 2} w={width * 0.45} />
        <Skeleton btr={20} bbr={20} h={width / 2} w={width * 0.45} />
        <Skeleton right={-40} btl={20} bbl={20} h={width / 2} w={width * 0.45} />
      </View>
    </View>
    <Skeleton h={30} mt={25} btl={20} btr={20} />
    <View flex={1} p={20}>
      <Skeleton h={60} br={10} />
      <Skeleton h={60} br={10} mt={14} />
      <Skeleton h={60} br={10} mt={14} />
      <Skeleton h={60} br={10} mt={14} />
      <Skeleton h={60} br={10} mt={14} />
    </View>
  </View>
);
