// import fileUpload from "express-fileupload";
import sharp from "sharp";
import blurhash, { encode } from "blurhash";
import { Random, UploadFile } from "@repo/lib";
export class ImageCompressionModule {
  static async generateBlurHash(file: UploadFile) {
    // if (!file.mimetype.startsWith("image/")) {
    //   throw new Error(
    //     "Only image files are supported for BlurHash generation.",
    //   );
    // }

    // Read the image buffer
    const imageBuffer = file.data;

    // Use sharp to resize the image and get raw pixel data
    const { data, info } = await sharp(imageBuffer)
      .resize(128, 128, { fit: "inside" }) // Resize to a smaller resolution for efficiency
      .raw()
      .ensureAlpha() // Ensure the image has an alpha channel
      .toBuffer({ resolveWithObject: true });

    // Generate the BlurHash from the pixel data
    const blurHash = encode(
      new Uint8ClampedArray(data), // Convert Buffer to Uint8ClampedArray
      info.width,
      info.height,
      9, // Number of X components
      9 // Number of Y components
    );

    return blurHash;
  }
  static async generateProfilePreview(image: UploadFile): Promise<UploadFile> {
    try {
      const buffer = await sharp(image.data)
        .resize({ width: 100, height: 100, fit: sharp.fit.cover })
        .toBuffer();

      return {
        name: image.name,
        mimetype: image.mimetype,
        data: buffer
      };
    } catch (error: any) {
      throw error;
    }
  }
  static async generalCompression(image: UploadFile): Promise<UploadFile> {
    try {
      const buffer = await sharp(image.data)
        .resize({ width: 400, fit: sharp.fit.cover })
        .toBuffer();

      return {
        name: image.name,
        mimetype: image.mimetype,
        data: buffer
      };
    } catch (error: any) {
      throw error;
    }
  }

  static async getCompressedImage(
    image: UploadFile,
    quality: "preview" | "medium"
  ): Promise<UploadFile> {
    const buffer = image.data;
    const format = "jpeg";
    const metadata = await sharp(buffer).metadata();
    if (!metadata.width || !metadata.height) {
      throw new Error("Unable to read image dimensions");
    }

    if (quality == "medium") {
      const resizeOptions = getTargetDimensions(metadata.width, metadata.height);

      const mainImage = await sharp(buffer)
        .resize(resizeOptions)
        .toFormat(format, { quality: 80 })
        .withMetadata()
        .toBuffer();
      return {
        data: mainImage,
        mimetype: "image/jpeg",
        name: `${Random.generateUUID()}.jpeg`
      };
    } else {
      const previewImage = await sharp(buffer)
        .resize({ width: 400, fit: "inside", withoutEnlargement: true })
        .toFormat(format, { quality: 30 })
        .withMetadata()
        .toBuffer();
      return {
        data: previewImage,
        mimetype: "image/jpeg",
        name: `${Random.generateUUID()}.jpeg`
      };
    }
  }
}

function getTargetDimensions(width: number, height: number) {
  const isPortrait = height > width;

  if (isPortrait) {
    if (height <= 1920 && width <= 1080) return undefined;
    return { height: 1920, fit: "inside" as const, withoutEnlargement: true };
  } else {
    if (width <= 1920 && height <= 1080) return undefined;
    return { width: 1920, fit: "inside" as const, withoutEnlargement: true };
  }
}
