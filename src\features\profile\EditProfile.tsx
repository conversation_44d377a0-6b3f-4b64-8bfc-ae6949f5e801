import GpsLocationPin from '@assets/svgs/gps-location.svg';
import PencilIcon from '@assets/svgs/pencil-icon.svg';
import { Button, Text, TextInput, View } from '@components/native';
import { Avatar, ScreenWrapper } from '@components/shared';
import { BounceTap, Switch } from '@components/shared/animated';
import { useAuth } from '@context/index';
import UsernameInput from '@features/user/component/UsernameInput';
import { router } from '@navigation/refs/navigation-ref';
import { GenderEnum } from '@packages/useStitch/types';
import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ScrollView } from 'react-native';

export const EditProfile = () => {
  const user = useAuth().session?.user!;
  const { control, handleSubmit, setValue, getValues } = useForm({
    defaultValues: {
      username: user?.username,
      location: user?.location,
      bio: user?.bio,
      playlistLink: user?.playlistLink,
      gender: user?.gender,
      dob: '',
      showMoodOnProfile: user?.showCurrentMoodOnProfile,
    },
  });

  return (
    <ScreenWrapper title="@lucabscott">
      <ScrollView style={{ padding: 20 }}>
        {/* Edit Avatar */}
        <View flexCenterColumn>
          <BounceTap onPress={() => router.navigate('SelectAvatar')}>
            <View>
              <Avatar withBounceTap={false} progress={100} showProgressRing={false} />
              <View flexCenterRow size={22} br={100} bg="background" bc="neutral20" bw={1} pos="absolute" right={-7} bottom={8}>
                <PencilIcon />
              </View>
            </View>
          </BounceTap>
        </View>
        {/* Username , Location , Bio ,  */}
        <View mt={20}>
          <UsernameInput initialValue={user?.username} onSuccess={v => setValue('username', v)} />
          <TextInput
            labelType="outer-left"
            control={control}
            label="Location"
            name="location"
            suffixIconAlwaysVisible
            suffixIcon={
              <View style={{ flex: 1 }} flexCenterRow>
                <GpsLocationPin height={18} />
              </View>
            }
          />
          <TextInput labelType="outer-left" multiline control={control} label="Bio" numberOfLines={4} name="bio" style={{ minHeight: 80 }} />
          <TextInput labelType="outer-left" control={control} label="Playlist Link" name="playlistLink" placeholder="Paste a playlist link here..." />
          <View>
            <View ml={2} mb={3}>
              <Text fs="12" fw="500" color="purple600">
                Gender (Optional)
              </Text>
            </View>
            <View bg="neutral00" br={20} px={20} py={14}>
              <View display="flex" fd="row" ai="center" jc="space-between">
                <Radio onPress={value => setValue('gender', value as GenderEnum)} label="Male" value="male" isActive={getValues('gender') == 'male'} />
                <Radio onPress={value => setValue('gender', value as GenderEnum)} label="Female" value="female" isActive={getValues('gender') == 'female'} />
                <Radio onPress={value => setValue('gender', value as GenderEnum)} label="Other" value="other" isActive={getValues('gender') == 'other'} />
              </View>
            </View>
          </View>
          <View mt={30} px={5} display="flex" fd="row" ai="center" jc="space-between">
            <Text color="neutral70" fs="12">
              Show current mood on profile
            </Text>
            <Controller
              control={control}
              name="showMoodOnProfile"
              render={({ field: { value, onChange } }) => (
                <Switch
                  isActive={value}
                  onToggle={newValue => {
                    onChange(newValue);
                    setValue('showMoodOnProfile', newValue);
                  }}
                />
              )}
            />
          </View>
        </View>
      </ScrollView>
      <View mb={14} p={20}>
        <Button onPress={() => {}}>Update</Button>
      </View>
    </ScreenWrapper>
  );
};

export type RadioProps = {
  value: string;
  label?: string;
  /**
   * Default false
   */
  isActive?: boolean;

  onPress?: (value: string) => void;
};
const Radio: React.FC<RadioProps> = ({ onPress, label, value, isActive }) => {
  return (
    <BounceTap onPress={() => onPress?.(value)}>
      <View flexCenterRow gap={5}>
        <View bw={1} bg="background" size={20} flexCenterRow br={100} bc="purple500">
          {isActive && <View size={10} br={100} bg="purple500" />}
        </View>
        {label && (
          <Text fs="14" color="neutral80" fw="500">
            {label}
          </Text>
        )}
      </View>
    </BounceTap>
  );
};
