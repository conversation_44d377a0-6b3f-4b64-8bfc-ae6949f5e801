export enum CHAT_STATUS {
  pending,
  accepted,
  rejected,
}

export type BestStitcherItem = {
  id: string;
  username: string;
  totalHealCount: string;
  avatar: string;
  chatStatus: CHAT_STATUS;
};

export type BestRecommenedStitcherResponse = BestStitcherItem[];

export type ChatRequestsItem = {
  id: string;
  status: CHAT_STATUS;
  user: {
    id: string;
    avatar: string;
    username: string;
  };
};
