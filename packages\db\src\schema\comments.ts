import {
  pgTable,
  uuid,
  text,
  timestamp,
  foreignKey,
  AnyPgColumn,
  index
} from "drizzle-orm/pg-core";
import { feedItemTable, postsTable } from "./feed.js";
import { users } from "./common.js";
export const commentsTable = pgTable(
  "comments",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    text: text("comment_text").notNull(),
    feedItemId: uuid("feed_item_id")
      .notNull()
      .references(() => feedItemTable.id, { onDelete: "cascade" }),
    replyTo: uuid("reply_to").references((): AnyPgColumn => commentsTable.id, {
      onDelete: "cascade"
    }),
    rootCommentId: uuid("root_comment_id").references((): AnyPgColumn => commentsTable.id, {
      onDelete: "cascade"
    }),
    editedAt: timestamp("edited_at"),
    deletedAt: timestamp("deleted_at"),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .$onUpdate(() => new Date())
  },
  (table) => [index("post_id_idx").on(table.feedItemId), index("reply_to_idx").on(table.replyTo)]
);
