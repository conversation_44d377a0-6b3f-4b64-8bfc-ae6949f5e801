import "@repo/lib/env";
import express from "express";
import type { Request, Response } from "express";
import loggerMiddleware from "@/middlewares/logger-middleware.js";
import { corsMiddleware } from "@/middlewares/cors-middleware.js";
import { createAuthRouter } from "./auth/router.js";
import { createUserRouter } from "./user/router.js";
import { errorMiddleware } from "@/middlewares/error-middleware.js";
import { notFoundMiddleware } from "@/middlewares/notfound-middleware.js";
import cookieParser from "cookie-parser";
import { createCountriesRouter } from "./public/router.js";
import { createFilesRouter } from "./files/router.js";
import fileUpload from "express-fileupload";
import { createNotificationRouter } from "./notification/router.js";
import { AuthMiddlewares } from "@/middlewares/auth-middleware.js";
import { fileURLToPath } from "url";
import path from "path";

import { createSandboxRouter } from "./sandbox/router.js";
import { checkAppUpdate } from "@/middlewares/check-app-version.js";
import { clientHeaderMiddleware } from "@/middlewares/client-header-middleware.js";
// import { createPostsRouter } from "./posts/router.js";
import { createCommentsRouter } from "./comments/router.js";
import { createMoodRouter } from "./mood/router.js";
import { createJournalRouter } from "./journal/router.js";
import { createChatRouter } from "./chat/routes.js";
import { createFeedRouter } from "./feed/router.js";

const app = express();
app.use(loggerMiddleware);
app.use(cookieParser());
app.use(express.urlencoded({ extended: true }));
app.use(express.json());
app.use(fileUpload());
app.use(corsMiddleware);
app.use(clientHeaderMiddleware);

app.use("/api/public", createCountriesRouter());
app.use("/api/auth", createAuthRouter());
app.use("/api/user", createUserRouter());
app.use("/api/notification", createNotificationRouter());
app.use("/api/feed", AuthMiddlewares.validateAccessToken, createFeedRouter());

app.use(
  "/api/journal",
  AuthMiddlewares.validateAccessToken,
  createJournalRouter(),
);

app.use(
  "/api/comment",
  AuthMiddlewares.validateAccessToken,
  createCommentsRouter(),
);
app.use("/api/mood", AuthMiddlewares.validateAccessToken, createMoodRouter());
app.use("/api/chat", AuthMiddlewares.validateAccessToken, createChatRouter());
// Need to be improved later

app.get("/api/app/check_update", checkAppUpdate);

app.use("/files", createFilesRouter());

app.use("/sandbox", createSandboxRouter());
app.get("/health", (req: Request, res: Response) => {
  res.statusCode = 200;
  res.json("OK");
  return;
});

// Get the directory name for ESModules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const distPath =
  process.env.NODE_ENV == "production"
    ? "../frontend"
    : "../../../../admin-panel/dist";

// Serve the React app's static files
const reactDistPath = path.resolve(__dirname, distPath);
// app.use(express.static(reactDistPath));
// app.get("*", (req, res) => {
//   res.sendFile(path.resolve(reactDistPath, "index.html"));
// });

app.use(notFoundMiddleware);
app.use(errorMiddleware);

export default app;
