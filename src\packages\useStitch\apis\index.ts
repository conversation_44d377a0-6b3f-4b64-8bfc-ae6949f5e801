import { exampleApiConfig, ExampleApis } from '../mock/example-apis';
import { authApiConfig, AuthApis } from './auth-apis';
import { chatApiConfig, ChatApis } from './chat-apis';
import { journalApiConfig, JournalApis } from './journal-apis';
import { moodApiConfig, MoodApis } from './mood-apis';
import { publicApiConfig, PublicApis } from './public-apis';
import { questionaireApiConfig, QuestionaireApis } from './questionaire-apis';
import { userApiConfig, UserApis } from './user-apis';

export const Apis = {
  ...ExampleApis,
  ...AuthApis,
  ...QuestionaireApis,
  ...JournalApis,
  ...UserApis,
  ...PublicApis,
  ...MoodApis,
  ...ChatApis,
};

export const apiConfig = {
  ...exampleApiConfig,
  ...authApiConfig,
  ...questionaireApiConfig,
  ...journalApiConfig,
  ...userApiConfig,
  ...publicApiConfig,
  ...moodApiConfig,
  ...chatApiConfig,
};

export type ApiConfigMap = typeof apiConfig;

export type EndpointKey = keyof typeof Apis;
