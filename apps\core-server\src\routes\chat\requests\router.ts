import { Router } from "express";
import { ChatRequestsController } from "./controller.js";

export function createChatRequestsRouter() {
  const router = Router();
  router.get('/list',ChatRequestsController.getPendingRequests);
  router.post('/:id',ChatRequestsController.requestUserForChat);
  router.post('/accept/:id',ChatRequestsController.acceptRequest)
  router.get("/reject/:id", ChatRequestsController.rejectRequest);
  return router;
}
