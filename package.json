{"name": "stitch", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "ios-dev": "ENVFILE=.env.dev react-native run-ios", "ios-prod": "ENVFILE=.env.prod react-native run-ios", "android-dev": "ENVFILE=.env.dev react-native run-android", "android-prod": "ENVFILE=.env.prod react-native run-android", "win-android-dev": "SET ENVFILE=.env.dev && react-native run-android", "win-android-prod": "SET ENVFILE=.env.prod && react-native run-android"}, "dependencies": {"@dr.pogodin/react-native-audio": "^1.15.0", "@gorhom/bottom-sheet": "^5.1.6", "@miblanchard/react-native-slider": "^2.6.0", "@notifee/react-native": "^9.1.8", "@nozbe/watermelondb": "^0.28.0", "@pontusab/react-native-media-library": "^1.0.6", "@react-native-camera-roll/camera-roll": "^7.10.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/messaging": "^22.4.0", "@react-native/codegen": "^0.80.1", "@react-native/new-app-screen": "0.80.1", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/drawer": "^7.5.3", "@react-navigation/material-top-tabs": "^7.2.15", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@react-navigation/stack": "^7.3.3", "@shopify/flash-list": "^1.8.3", "@simform_solutions/react-native-audio-waveform": "^2.1.5", "@tanstack/react-query": "^5.83.0", "aws-amplify": "^6.15.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "expensify-common": "2.0.115", "html-entities": "^2.6.0", "lodash": "^4.17.21", "moti": "^0.30.0", "node-emoji": "^2.2.0", "react": "19.1.0", "react-hook-form": "^7.57.0", "react-native": "0.80.1", "react-native-audio-api": "^0.6.4", "react-native-audio-recorder-player": "^3.6.13", "react-native-bootsplash": "^6.3.10", "react-native-calendars": "^1.1313.0", "react-native-circular-progress-indicator": "^4.4.2", "react-native-collapsible-tab-view": "^8.0.1", "react-native-config": "^1.5.5", "react-native-dialog": "^9.3.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.27.1", "react-native-image-picker": "^8.2.1", "react-native-image-viewing": "^0.2.2", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-material-ripple": "^0.9.1", "react-native-pager-view": "^6.8.1", "react-native-permissions": "^5.4.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.11.1", "react-native-size-matters": "^0.4.2", "react-native-super-grid": "^6.0.1", "react-native-svg": "^15.12.0", "react-native-svg-transformer": "^1.5.1", "react-native-tab-view": "^4.1.2", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.16.1", "react-native-vision-camera": "^4.6.4", "rn-fetch-blob": "^0.12.0", "rxjs": "^7.8.2", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.28.0", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.20", "@types/react": "^19.1.0", "@types/react-native-material-ripple": "^0.9.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "eslint-plugin-react": "^7.37.5", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-dotenv": "^3.4.11", "react-native-rename": "^3.2.17", "react-test-renderer": "19.1.0", "reactotron-react-native": "^5.1.14", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}