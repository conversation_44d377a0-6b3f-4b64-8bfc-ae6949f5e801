import React from 'react';
import Animated, { Easing, useAnimatedProps, SharedValue } from 'react-native-reanimated';
import { Path } from 'react-native-svg';

const AnimatedPath = Animated.createAnimatedComponent(Path);

interface Props {
  d: string;
  strokeWidth: number;
  animatedStrokeColor: string;
  progress: SharedValue<number>;
}

const AnimatedStroke = ({ d, strokeWidth, animatedStrokeColor, progress }: Props) => {
  const [length, setLength] = React.useState(0);

  // Callback ref to access the underlying SVG Path node
  const handlePathRef = React.useCallback((node: any) => {
    if (node) {
      try {
        const pathLength = node.getTotalLength();
        setLength(pathLength);
      } catch (e) {
        // getTotalLength may not be available, ignore
      }
    }
  }, []);

  const animatedProps = useAnimatedProps(() => ({
    strokeDashoffset: length - length * Easing.bezierFn(0.37, 0, 0.63, 1)(progress.value),
  }));

  const animatedBGProps = useAnimatedProps(() => ({
    strokeDashoffset: length - length * Easing.bezierFn(0.61, 1, 0.88, 1)(progress.value),
    fillOpacity: progress.value,
  }));

  return (
    <>
      <AnimatedPath animatedProps={animatedBGProps} d={d} stroke={animatedStrokeColor} strokeWidth={strokeWidth} fill="white" strokeDasharray={length} />
      <AnimatedPath ref={handlePathRef} animatedProps={animatedProps} d={d} stroke="#000" strokeWidth={strokeWidth} strokeDasharray={length} />
    </>
  );
};

export default AnimatedStroke;
