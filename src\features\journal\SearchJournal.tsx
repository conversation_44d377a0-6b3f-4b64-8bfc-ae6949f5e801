import ClockIcon from '@assets/svgs/clock-icon.svg';
import SearchIcon from '@assets/svgs/search-icon.svg';
import { Button, Pressable, Text, TextInput, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { LoaderCentered } from '@components/shared/animated';
import { useAuth, useTheme } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { FlashList } from '@shopify/flash-list';
import React from 'react';
import { useForm } from 'react-hook-form';
import { Dimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
const { width } = Dimensions.get('screen');

export const SearchJournal = () => {
  const { session } = useAuth();
  console.log(session);
  const { colors } = useTheme();
  const { data, isLoading } = useStitch('journalSearchHistoryList');

  const { bottom } = useSafeAreaInsets();
  const { control, handleSubmit, setValue } = useForm({
    defaultValues: {
      query: '',
    },
  });

  const searchInput = (
    <View flex={1} my={10} px={20}>
      <TextInput
        autoFocus
        control={control}
        name="query"
        label="Search"
        labelType="background"
        gapBottom={5}
        onSubmitEditing={e => {
          // this should be called when user taps of search in keyboard
          // call search function from here or when user taps on search button below..
          console.log('Submitted >>>');
        }}
        suffixIconAlwaysVisible
        returnKeyType="search"
        suffixIcon={<SearchIcon style={{ marginRight: 5 }} stroke={colors.neutral40} />}
        prefixIcon={
          <Pressable pt={2} onPress={router.back}>
            <Ionicons name="arrow-back-outline" color={colors.neutral70} size={22} />
          </Pressable>
        }
      />
    </View>
  );

  return (
    <ScreenWrapper
      headerStyles={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}
      leftHeaderProps={{ px: 0 }}
      hideBackButton
      customHeaderComponent={searchInput}
      py={20}>
      {isLoading ? (
        <LoaderCentered />
      ) : data?.data.length == 0 ? (
        <View flex={1} flexCenterRow>
          <Text mt={-80} fs="14" fw="500" color="neutral80">
            No Search History
          </Text>
        </View>
      ) : (
        <FlashList
          contentContainerStyle={{ paddingBottom: 100 }}
          data={data?.data}
          renderItem={({ index, item }) => {
            return (
              <View key={index} px={20} py={6} display="flex" fd="column" gap={8}>
                <View fd="row" display="flex" ai="center" gap={10}>
                  <ClockIcon stroke={colors.neutral60} />
                  <Text>{item.text}</Text>
                </View>

                <View h={1} mt={4} bg="neutral10" />
              </View>
            );
          }}
        />
      )}
      <View disableSizeMatter px={20} pos="absolute" w={width} bottom={bottom > 10 ? bottom : bottom + 10}>
        <Button
          onPress={() => {
            router.back();
          }}>
          Search
        </Button>
      </View>
    </ScreenWrapper>
  );
};
