import { ThemeColorKeys } from '@/types/color-types';
import { View } from '@components/native';
import { useTheme } from '@context/index';
import React from 'react';
import { Image, StyleSheet } from 'react-native';
import { CircularProgressBase } from 'react-native-circular-progress-indicator';
import { BounceTap } from './animated';

export interface AvatarProps {
  /**
   * Default 80
   */
  size?: number;
  onPress?: () => void;
  gradientColors?: string[]; // Optional gradient border colors
  bg?: ThemeColorKeys; // Theme-based background color
  borderWidth?: number; // Customizable border width
  imageSource?: string | number; // Add image source prop for flexibility
  progress?: number;
  progressSize?: number;
  showProgressRing?: boolean;
  progressBarPrimaryColor?: string;
  progressBarSecondaryColor?: string;
  /**
   * Default to true
   */
  withBounceTap?: boolean;
}

export const Avatar: React.FC<AvatarProps> = ({
  size = 80,
  onPress,
  bg = 'background',
  gradientColors,
  borderWidth = 2,
  progress = 0,
  progressSize = 45,
  withBounceTap = true,
  showProgressRing = true,
  imageSource = require('@assets/images/avatar-1.png'),
}) => {
  const { colors } = useTheme();
  const innerSize = size - borderWidth * 2;

  let BaseView = (
    <View
      jc="center"
      ai="center"
      display="flex"
      br={innerSize / 2}
      style={{
        width: innerSize,
        height: innerSize,
        borderRadius: innerSize / 2,
        backgroundColor: colors[bg] || colors.background,
      }}>
      <Image
        source={imageSource}
        style={{
          width: gradientColors ? innerSize * 0.88 : innerSize,
          height: gradientColors ? innerSize * 0.88 : innerSize,
          borderRadius: 999,
        }}
        resizeMode="cover"
      />
    </View>
  );

  if (showProgressRing) {
    BaseView = (
      <CircularProgressBase
        value={progress}
        activeStrokeWidth={8}
        inActiveStrokeWidth={0}
        rotation={180}
        activeStrokeSecondaryColor={'#FAFF9F'}
        activeStrokeColor={'#D5CB0E'}
        inActiveStrokeColor="transparent"
        radius={progressSize}>
        {BaseView}
      </CircularProgressBase>
    );
  }

  if (withBounceTap) {
    BaseView = (
      <BounceTap size={size} onPress={onPress}>
        {BaseView}
      </BounceTap>
    );
  }

  return (
    <View
      style={{
        width: size,
        height: size,
        borderRadius: 999,
        backgroundColor: colors[bg] || colors.background,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
      {BaseView}
    </View>
  );
};

const styles = StyleSheet.create({
  gradientContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
