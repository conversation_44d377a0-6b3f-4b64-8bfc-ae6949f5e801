import { Router } from "express";
import { FeedController } from "./controller.js";

export function createFeedRouter() {
  const router = Router();
  router.post("/create-post", FeedController.createPost);
  router.post("/create-poll", FeedController.createPoll);
  router.post("/like/:id", FeedController.likeFeedItem);
  router.post("/bookmark/:id", FeedController.bookmarkFeedItem);
  router.post("/vote/:id", FeedController.votePoll);
  router.get("/list", FeedController.getFeed);
  return router;
}
