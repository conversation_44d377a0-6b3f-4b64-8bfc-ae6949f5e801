import { Text } from '@components/native';
import { useTheme } from '@context/Theme/ThemeContext';
import { AnimatePresence, MotiView } from 'moti';
import React, { createContext, useCallback, useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';

export type NotificationType = 'center' | 'top' | 'bottom';
export type NotificationStatus = 'info';

export interface Notification {
  id: string;
  message: string;
  type: NotificationType;
  status: NotificationStatus;
  duration?: number;
  ta?: 'center' | 'left' | 'right';
}

export interface Notifier {
  center(message: string, status?: NotificationStatus, duration?: number): void;
  top(message: string, status?: NotificationStatus, duration?: number): void;
  bottom(message: string, status?: NotificationStatus, duration?: number): void;
  clear(): void;
}

const NotifierContext = createContext<Notifier | undefined>(undefined);

const defaultDuration = 3000; // 3 seconds
const animationDuration = 300; // 300ms for fade in/out

const generateId = () => Math.random().toString(36).substr(2, 9);

class NotificationController {
  private queue: Notification[] = [];
  private setNotifications: React.Dispatch<React.SetStateAction<Notification[]>> | null = null;
  private isProcessing = false;

  register(setNotifications: React.Dispatch<React.SetStateAction<Notification[]>>) {
    this.setNotifications = setNotifications;
    this.processQueue();
  }

  private addNotification(message: string, type: NotificationType, status: NotificationStatus = 'info', duration: number = defaultDuration, ta: 'center' | 'left' | 'right' = 'left') {
    const notification: Notification = {
      id: generateId(),
      message,
      type,
      status,
      duration,
      ta,
    };
    this.queue.push(notification);
    this.processQueue();
  }

  center(message: string, status: NotificationStatus = 'info', duration: number = defaultDuration, ta: 'center' | 'left' | 'right' = 'left') {
    this.addNotification(message, 'center', status, duration, ta);
  }

  top(message: string, status: NotificationStatus = 'info', duration: number = defaultDuration, ta: 'center' | 'left' | 'right' = 'left') {
    this.addNotification(message, 'top', status, duration, ta);
  }

  bottom(message: string, status: NotificationStatus = 'info', duration: number = defaultDuration, ta: 'center' | 'left' | 'right' = 'left') {
    this.addNotification(message, 'bottom', status, duration, ta);
  }

  clear() {
    this.queue = [];
    this.setNotifications?.([]);
  }

  private async processQueue() {
    if (this.isProcessing || !this.setNotifications) return;

    this.isProcessing = true;

    while (this.queue.length > 0) {
      const notification = this.queue[0];
      this.setNotifications([notification]);

      await new Promise(resolve => setTimeout(resolve, (notification.duration || defaultDuration) + animationDuration));

      this.queue.shift();
      this.setNotifications([]);
    }

    this.isProcessing = false;
  }
}

export const notify = new NotificationController();

const NotificationComponent: React.FC<{ notification: Notification }> = ({ notification }) => {
  const { colors } = useTheme();

  const positionStyle = {
    center: {
      top: '50%',
      left: 0,
      right: 0,
      transform: [{ translateY: -50 }],
      borderRadius: 12,
    },
    top: {
      top: 50,
      left: 0,
      right: 0,
      borderRadius: 16,
    },
    bottom: {
      bottom: 20,
      left: 0,
      right: 0,
      borderRadius: 16,
    },
  }[notification.type] as any;

  const statusStyle = {
    info: {
      backgroundColor: colors.transparent,
    },
  }[notification.status];

  const translateYFrom = notification.type === 'top' ? -30 : 30;
  const translateYExit = notification.type === 'top' ? -30 : 30;

  return (
    <MotiView
      from={{
        opacity: 0.2,
        translateY: translateYFrom,
      }}
      animate={{
        opacity: 1,
        translateY: 0,
      }}
      exit={{
        opacity: 0.2,
        translateY: translateYExit,
      }}
      transition={{
        type: 'timing',
        duration: animationDuration,
      }}
      style={[styles.container, positionStyle, statusStyle]}>
      <MotiView style={[styles.subContainer, { backgroundColor: colors.neutral80, maxWidth: notification.type == 'center' ? '60%' : null }]}>
        <Text color="neutral20" ta={notification.ta ?? 'left'} fs="14" fw="500">
          {notification.message}
        </Text>
      </MotiView>
    </MotiView>
  );
};

export const NotifierProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const registerController = useCallback(() => {
    notify.register(setNotifications);
  }, []);

  useEffect(() => {
    registerController();
  }, [registerController]);

  return (
    <NotifierContext.Provider value={notify}>
      {children}
      <AnimatePresence>
        {notifications.map(notification => (
          <NotificationComponent key={notification.id} notification={notification} />
        ))}
      </AnimatePresence>
    </NotifierContext.Provider>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    width: '100%',
    backgroundColor: 'red',
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
  },
  subContainer: {
    shadowColor: '#000',
    borderRadius: 12,
    width: '95%',
    paddingVertical: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    paddingHorizontal: 16,
    shadowRadius: 4,
    elevation: 5,
  },
  info: {
    backgroundColor: 'rgba(255, 251, 251, 0.85)',
  },
  text: {
    color: '#000',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});
