import { Button, Image, Text, TextInput, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { LoaderCentered } from '@components/shared/animated';
import { notify } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { JournalCategoryCoverItem } from '@packages/useStitch/types';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Dimensions, Pressable, ScrollView } from 'react-native';
import { SimpleGrid } from 'react-native-super-grid';
import EntypoIcon from 'react-native-vector-icons/Entypo';

const { width } = Dimensions.get('screen');
const CARD_WIDTH = width / 3 - 50;

export const CreateJournalType = () => {
  const { data, isLoading, isError } = useStitch('journalCategoryCovers');
  const { refetch } = useStitch('journalCategoryList', { queryOptions: { select: false } as any });
  const { mutate, isMutating } = useStitch('createJournalCategory', {
    mutationOptions: {
      onSuccess: async () => {
        notify.top('Journal category created successfully.');
        await refetch();
        router.back();
      },
    },
  });
  const [selectedItem, setSelectedItem] = useState<JournalCategoryCoverItem | null>(null);
  const { control, handleSubmit, setValue } = useForm({
    defaultValues: {
      albumTitle: '',
    },
  });
  const categories = data?.data ?? [];

  const onSubmit = ({ albumTitle }: { albumTitle: string }) => {
    mutate({ coverId: selectedItem!.id, title: albumTitle, type: albumTitle.toLowerCase() });
  };

  return (
    <ScreenWrapper isMutating={isMutating} title="Create new album">
      <View flex={1} p={20}>
        <TextInput
          rules={{
            required: 'Album title is required',
          }}
          control={control}
          name="albumTitle"
          label="Album Title"
          labelType="background"
          onSuffixIconPress={() => setValue('albumTitle', '')}
        />
        <Text ml={8} fw="500" mt={20}>
          Choose Album Cover
        </Text>
        <View flex={1} mt={15}>
          {isLoading ? (
            <LoaderCentered />
          ) : (
            <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
              <SimpleGrid
                listKey="create-journal-type"
                itemDimension={CARD_WIDTH}
                spacing={12}
                data={categories}
                renderItem={({ item, index }) => {
                  const isSelected = selectedItem?.id == item.id;
                  return (
                    <Pressable onPress={() => setSelectedItem(item)} key={index}>
                      <View bw={2} bc={isSelected ? 'positive60' : 'transparent'} shadow="md" mr={8} mb={10} btr={20} bbr={20} h={CARD_WIDTH * 1.2} bg="lightBlue">
                        <Image objectFit="cover" w="100%" h="100%" btr={20} bbr={20} source={{ uri: item.url }} />
                        {isSelected && (
                          <View size={18} br={50} flexCenterRow bg="positive50" pos="absolute" right={10} top={10}>
                            <EntypoIcon name="check" color="white" />
                          </View>
                        )}
                      </View>
                    </Pressable>
                  );
                }}
              />
            </ScrollView>
          )}
        </View>
        <Button isLoading={isMutating} my={20} onPress={handleSubmit(onSubmit)}>
          SAVE
        </Button>
      </View>
    </ScreenWrapper>
  );
};
