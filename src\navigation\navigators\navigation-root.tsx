import { useSocket } from '@context/index';
import { linking } from '@navigation/configs/linking';
import { navigationRef } from '@navigation/refs/navigation-ref';
import { NavigationContainer } from '@react-navigation/native';
import AppNavigator from './app-navigator';

const RootNavigation = () => {
  const { onNavigationStateChange } = useSocket();
  return (
    <NavigationContainer linking={linking} onStateChange={onNavigationStateChange} ref={navigationRef}>
      <AppNavigator />
    </NavigationContainer>
  );
};

export default RootNavigation;
