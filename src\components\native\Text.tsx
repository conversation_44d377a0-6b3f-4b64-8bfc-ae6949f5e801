import type { ThemeColorKeys } from '@/types/color-types';
import type { FF, FS, FW } from '@/types/layout-types';
import { useTheme } from '@context/Theme/ThemeContext';
import { forwardRef } from 'react';
import { Text as RNText, TextProps as RNTextProps, TextStyle, View } from 'react-native';
import Animated from 'react-native-reanimated';
import { ms } from 'react-native-size-matters';

export interface TextProps extends RNTextProps {
  /**
   * Font Size
   */
  fs?: FS;
  /**
   * Font Family
   */
  ff?: FF;
  /**
   * Color enums eg "Foreground" , "Background" , etc..
   */
  color?: ThemeColorKeys;
  /**
   * Font Weight
   */
  fw?: FW;
  /**
   * Text Align
   */
  ta?: 'center' | 'justify' | 'left' | 'right' | 'auto';
  /**
   * Margin Left
   */
  ml?: number;
  /**
   * Margin Right
   */
  mr?: number;
  /**
   * Margin Top
   */
  mt?: number;
  /**
   * Margin Bottom
   */
  mb?: number;
  /**
   * Margin Horizontal
   */
  mx?: number;
  /**
   * Margin Vertical
   */
  my?: number;
  /**
   * Text Decoration Line
   */
  tdl?: 'none' | 'underline' | 'line-through' | 'underline line-through';
  /**
   * Text Decoration Line Gap (in pixels)
   */
  tdlg?: number;

  /**
   * Text transform
   */
  tt?: 'none' | 'capitalize' | 'uppercase' | 'lowercase' | undefined;
}

export const FontFamilyMap = {
  '300': 'Montserrat-Light',
  '400': 'Montserrat-Regular',
  '500': 'Montserrat-Medium',
  '600': 'Montserrat-SemiBold',
  '700': 'Montserrat-Bold',
} satisfies Record<FW, FF>;

export const Text = forwardRef<RNText, TextProps>(
  (
    {
      fw = '400',
      fs = '16',
      color = 'text',
      style,
      ff,
      ta,
      ml,
      mr,
      mt,
      mb,
      mx,
      tdl = 'none',
      tdlg = 2, // Default gap of 2 pixels
      my,
      tt,
      ...props
    },
    ref,
  ) => {
    const { colors } = useTheme();
    const fontSize = ms(Number(fs), 0.5);

    // Text styles without native textDecorationLine
    const textStyles: TextStyle = {
      fontSize,
      color: colors[color],
      fontFamily: ff ?? FontFamilyMap[fw],
      fontWeight: fw,
      textAlign: ta,
      marginLeft: ml,
      marginRight: mr,
      marginTop: mt,
      marginBottom: mb,
      marginHorizontal: mx,
      marginVertical: my,
      textTransform: tt,
    };

    // Calculate decoration line styles
    const hasUnderline = tdl === 'underline' || tdl === 'underline line-through';
    const hasLineThrough = tdl === 'line-through' || tdl === 'underline line-through';

    const decorationLineStyle: TextStyle = {
      position: 'absolute',
      left: 0,
      right: 0,
      height: 1, // Line thickness
      backgroundColor: colors[color], // Match text color
    };

    // Underline styles
    const underlineStyle = hasUnderline
      ? {
          ...decorationLineStyle,
          bottom: -tdlg, // Position below text with gap
        }
      : null;

    // Strikethrough styles
    const lineThroughStyle = hasLineThrough
      ? {
          ...decorationLineStyle,
          top: fontSize / 2, // Center vertically through text
        }
      : null;

    return (
      <View style={{ position: 'relative' }}>
        <RNText ref={ref} style={[textStyles, style]} {...props}>
          {props.children}
        </RNText>
        {/* Custom underline */}
        {hasUnderline && <View style={underlineStyle} />}
        {/* Custom strikethrough */}
        {hasLineThrough && <View style={lineThroughStyle} />}
      </View>
    );
  },
);

Text.displayName = 'Text';

export const AnimatedText = Animated.createAnimatedComponent(Text);
AnimatedText.displayName = 'AnimatedText';
