import PlusIcon from '@assets/svgs/plus-icon.svg';
import TickPolygonIcon from '@assets/svgs/tick-polygon-icon.svg';
import { Button, Text, View } from '@components/native';
import { ScreenWrapper, SuccessDialog } from '@components/shared';
import { BounceTap, LoaderCentered } from '@components/shared/animated';
import { notify, useTheme } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { JournalCategoryItem } from '@packages/useStitch/types';
import React, { useState } from 'react';
import { Dimensions, Image, ImageStyle, Platform, Pressable, ScrollView } from 'react-native';
import { SimpleGrid } from 'react-native-super-grid';
import RNFetchBlob from 'rn-fetch-blob';
import { useJournalStore } from './store/journal-store';

const addJournalObj = { type: 'add-journal', bgImage: '', id: '', title: '', totalCount: 0 };
const { width, height } = Dimensions.get('screen');
const CARD_WIDTH = width / 3 - 50;
const MAX_CATEGORIES = 9;

export const ChooseJournalType = () => {
  const [selectedItem, setSelectedItem] = useState<JournalCategoryItem | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { isLoading, data, refetch: refetchCatList } = useStitch('journalCategoryList');
  const queryParams = selectedItem?.title.toLowerCase() === 'all' ? {} : { category: selectedItem?.id };
  const { refetch } = useStitch('journalFeedListByCategory', { queryOptions: { enabled: false } as any, queryParams });
  const { refetch: refetchAllCategory } = useStitch('journalFeedListByCategory', {
    queryOptions: { enabled: false } as any,
    queryParams: {},
  });
  const { mutateAsync, isMutating } = useStitch('createJournal', {
    mutationOptions: {
      onSuccess: async () => {
        setIsDialogOpen(true);
        await refetch();
        setTimeout(async () => {
          setIsDialogOpen(false);
          router.pop(2);
          refetchCatList();
          refetchAllCategory();
        }, 4000);
      },
    },
  });
  const { tokens, title } = useJournalStore();
  let categories = data?.data ?? [];
  if (categories.length < MAX_CATEGORIES) {
    categories = [...categories, addJournalObj];
  }

  const onSaveClick = async () => {
    if (!selectedItem) {
      notify.bottom('Select your journal category.');
      return;
    }
    try {
      const formData = new FormData();
      formData.append('title', title);
      let summary = '';
      const parsedTokens = tokens.map(token => {
        if (token.type == 'text-input' && token.value != '' && token.value.toString().length > 0) {
          summary = summary + ' ' + token.value;
        }
        return { ...token, ref: null, asset: null };
      });
      formData.append('description', JSON.stringify(parsedTokens));
      formData.append('category', selectedItem!.id);
      formData.append('summary', summary);
      if (tokens.find(token => token.type === 'image-file')) {
        const file = tokens.find(token => token.type === 'image-file')!.asset!;
        const uri = Platform.OS == 'ios' ? file.uri!.replace('file://', '') : file.uri!;
        const response = await RNFetchBlob.fs.readFile(uri, 'base64');
        const blob = {
          uri: uri,
          name: file.fileName,
          type: file.type,
          data: response,
        };
        formData.append('image', blob as any);
      }
      if (tokens.find(token => token.type == 'audio-file')) {
        const token = tokens.find(token => token.type === 'audio-file');
        const uri = Platform.OS == 'ios' ? token!.value.toString().replace('file://', '') : token!.value.toString()!;
        const response = await RNFetchBlob.fs.readFile(uri, 'base64');
        const blob = {
          uri: uri,
          name: token?.value.toString().split('/').pop(),
          data: response,
        };
        formData.append('audio', blob as any);
      }

      await mutateAsync(formData);
    } catch (error) {
      console.log('Create Jorunal Error ', error);
    }
  };

  return (
    <>
      <ScreenWrapper title="My Journal">
        <View flexCenterColumn flex={0.8}>
          <View w={CARD_WIDTH + 10} h={CARD_WIDTH * 1.3} btr={20} bbr={20} bc="neutral30" bw={1}>
            <Image
              style={{
                height: '100%',
                width: '100%',
                borderTopRightRadius: 20,
                borderBottomRightRadius: 20,
              }}
              source={{ uri: selectedItem?.bgImage }}
            />
            <View pos="absolute" left={'60%'} top={'20%'}>
              <TickPolygonIcon />
            </View>
          </View>
          {selectedItem && (
            <View flexCenterRow mt={25}>
              <Text fw="600" fs="20" color="neutral70" ff="PlayfairDisplay-Medium">
                Save to{' '}
              </Text>
              <Text fw="600" fs="20" tt="capitalize" color="neutral90" ff="PlayfairDisplay-SemiBold">
                {selectedItem?.type} Journal ?
              </Text>
            </View>
          )}
        </View>
        <View flex={1.2} btl={30} btr={30} bg="orange" display="flex" px={20} py={30} jc="space-between">
          <Text fw="500">Choose a journal to save</Text>
          <ScrollView>
            {isLoading ? (
              <View mih={height / 3.5}>
                <LoaderCentered />
              </View>
            ) : (
              <SimpleGrid
                listKey={'journal-type'}
                itemDimension={CARD_WIDTH}
                data={categories}
                renderItem={({ item, index }) => {
                  const imgStyles = {
                    position: 'absolute',
                    objectFit: 'cover',
                    top: 0,
                    left: 0,
                    height: '100%',
                    width: '100%',
                    borderTopRightRadius: 20,
                    borderBottomRightRadius: 20,
                  } as ImageStyle;
                  if (item.type == 'add-journal') {
                    return <AddJournalButton />;
                  }
                  return (
                    <Pressable key={index} onPress={() => setSelectedItem(item)}>
                      <View
                        mr={5}
                        mb={5}
                        btr={20}
                        bbr={20}
                        bw={selectedItem?.id == item.id ? 2 : 0}
                        bc={selectedItem?.id == item.id ? 'positive60' : 'transparent'}
                        h={CARD_WIDTH * 1.2}
                        bg="lightBlue">
                        <Image style={imgStyles} source={{ uri: item.bgImage }} />
                      </View>
                    </Pressable>
                  );
                }}
              />
            )}
          </ScrollView>
          <Button isLoading={isMutating} h={46} onPress={onSaveClick}>
            Save
          </Button>
        </View>
      </ScreenWrapper>
      <SuccessDialog isOpen={isDialogOpen} />
    </>
  );
};

const AddJournalButton = () => {
  const { colors } = useTheme();
  return (
    <Pressable onPress={() => router.navigate('CreateJournalType')}>
      <View btr={20} bbr={20} h={CARD_WIDTH * 1.2} bg="lightBlue" flexCenterRow>
        <BounceTap>
          <View bc="purpleLight" bw={2} mr={5} mb={5} br={100} flexCenterRow size={CARD_WIDTH / 1.5} bg="purple100">
            <PlusIcon fill={colors.purple500} />
          </View>
        </BounceTap>
      </View>
    </Pressable>
  );
};
