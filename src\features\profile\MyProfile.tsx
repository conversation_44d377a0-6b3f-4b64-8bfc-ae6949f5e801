import { <PERSON><PERSON>, SafeAreaView, Text, View } from '@components/native';
import { Avatar } from '@components/shared';
import { Ripple } from '@components/shared/animated';
import { useAuth } from '@context/index';
import { router } from '@navigation/refs/navigation-ref';
import { FlashList } from '@shopify/flash-list';
import React from 'react';

export const MyProfileScreen = () => {
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <FlashList
        onRefresh={() => {}}
        refreshing={false}
        style={{ backgroundColor: 'red' }}
        ListHeaderComponent={ProfileHeader}
        data={Array.from({ length: 0 })}
        renderItem={({ item, index }) => (
          <View>
            <Text>item {index}</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

const ProfileHeader = () => {
  const user = useAuth()?.session?.user!;

  return (
    <View p={20}>
      <View display="flex" fd="row" jc="space-between" ai="center">
        {/* Avatar with profile percentage */}
        <View flexCenterColumn>
          <Avatar size={60} progressSize={38} progress={60} />
          <Text fs="10" mt={20} color="neutral80">
            {user?.profilePercentage ?? 0}% profile completed
          </Text>
          {/* <Text>{user. }</Text> */}
        </View>

        {/* Posts , Followers  */}
        <View flex={1}>
          <View display="flex" fd="row" jc="space-evenly">
            <Ripple style={{ padding: 20 }}>
              <View flexCenterColumn gap={5}>
                <Text fw="600" fs="20" color="neutral80">
                  {user?.totalPostCount}
                </Text>
                <Text fs="12" color="neutral80">
                  Posts
                </Text>
              </View>
            </Ripple>
            <Ripple style={{ padding: 20 }}>
              <View flexCenterColumn gap={5}>
                <Text fw="600" fs="20" color="neutral80">
                  {user?.totalFollowers ?? 0}
                </Text>
                <Text fs="12" color="neutral80">
                  Followers
                </Text>
              </View>
            </Ripple>
          </View>
        </View>
      </View>

      {/* Badges ( optional ) */}
      {/* <View my={12}>
        <Text>Badges here..</Text>
      </View> */}

      {/* Bio / Description */}
      {user.bio !== '' && user.bio != undefined && (
        <View my={5}>
          <Text color="neutral80" fs="12" fw="600">
            Bio/Description
          </Text>
          <Text color="neutral70" fs="12" my={6}>
            {user?.bio}
          </Text>
        </View>
      )}

      {user.playlistLink == '' && user.playlistLink != undefined && (
        <View my={5}>
          <Text color="neutral80" fs="12" fw="600">
            Playlist link
          </Text>
          <Text color="neutral70" fs="12" my={6}>
            {user?.playlistLink}
          </Text>
        </View>
      )}

      <Button my={16} onPress={() => router.navigate('EditProfile')}>
        Edit Profile
      </Button>
      <View h={0.5} bg="neutral10" />
      {/* {user?.totalPostCount > 0 && ( */}
      <Text my={10} fw="500" fs="14" color="neutral80">
        Recent Posts
      </Text>
      {/* )} */}
    </View>
  );
};
