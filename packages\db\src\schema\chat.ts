import { json, pgEnum, pgTable, text, timestamp, uuid, index } from "drizzle-orm/pg-core";
import { mediaTypeEnum, users } from "./common.js";

export type Attachment = {
  type: (typeof mediaTypeEnum.enumValues)[number];
  url: string;
  preview: string;
};

export const chatMessageTable = pgTable("chat_messages", {
  id: uuid("id").primaryKey().defaultRandom(),
  roomId: uuid("room_id")
    .references(() => chatRoomTable.id, { onDelete: "cascade" })
    .notNull(),
  senderId: uuid("sender_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull(),
  message: text("message").notNull(),
  attachments: json("attachments").$type<Attachment>().array(),
  sentAt: timestamp("sent_at").defaultNow(),
  linkPreview: json("link_preview"),
  deliveredAt: timestamp("delivered_at"),
  seenAt: timestamp("seen_at"),
});

export const conversationTypeEnum = pgEnum("conversation_type_enum", ["personal", "booking"]);

export const chatRoomTable = pgTable(
  "chat_rooms",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    roomId: text("roomId").notNull().unique(),
    user1: uuid("user1")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    user2: uuid("user2")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    listingId: uuid("listing_id"),
    blockedBy: uuid("blocked_by").references(() => users.id, {
      onDelete: "set null"
    }),
    // for clear chat and fetch records after that time (later implementation)
    clearTimestamps: json("clear_timestamps").$type<Record<string, string | null>>().default({}),
    pinnedBy: json("pinned_by").$type<Record<string, string | null>>().default({}),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .$onUpdate(() => new Date())
  },
  (table) => [index("room_id_idx").on(table.roomId)]
);

export const chatRequestStatusEnum = pgEnum("chat_request_status_enum", [
  "pending",
  "accepted",
  "rejected"
]);

export const chatRequestsTable = pgTable(
  "chat_requests",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    requestedUserId: uuid("requested_user_id")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    status: chatRequestStatusEnum("status").default("pending").notNull(),
    createdAt: timestamp("created_at").defaultNow(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .$onUpdate(() => new Date())
  },
  (table) => [
    index("requested_user_id_idx").on(table.requestedUserId),
    index("status_idx").on(table.status)
  ]
);
