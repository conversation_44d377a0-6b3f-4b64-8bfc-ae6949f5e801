import { ApiResponse, QueryApiConfig } from '../types';
import { GetCountryListApiResponse } from '../types/public-api-types';

export namespace PublicApis {
  export const getCountryList = 'get-country-list' as const;
}

export const publicApiConfig = {
  getCountryList: {
    path: '/public/get-countries',
    method: 'GET',
    protected: false,
    responseType: undefined as unknown as ApiResponse<GetCountryListApiResponse>,
    baseCacheKey: 'get-countries',
    staleTime: 0,
    type: 'query' as const,
    defaultData: { data: [] },
  } satisfies QueryApiConfig<GetCountryListApiResponse>,
};
