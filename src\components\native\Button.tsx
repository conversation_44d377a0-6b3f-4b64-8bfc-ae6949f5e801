import { Ring<PERSON>pinner } from '@components/shared/animated';
import { useTheme } from '@context/Theme/ThemeContext';

import { ThemeColorKeys } from '@/types/color-types';
import { FS, FW } from '@/types/layout-types';
import { ReactElement, forwardRef, isValidElement } from 'react';
import { PressableProps, Pressable as RNPressable, TextStyle, View, ViewStyle } from 'react-native';
import Animated, { interpolateColor, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import { ms, mvs } from 'react-native-size-matters';
import { Text } from './Text';
const DURATION = 500;

// Utility function to darken a hex color
// !! Needed for theme based apps instead we need to manually give the darken hex code for each
const darkenColor = (hex: string, factor: number = 0.8): string => {
  // Remove '#' if present and handle shorthand hex (e.g., #RGB)
  hex = hex.replace(/^#/, '');
  if (hex.length === 3) {
    hex = hex
      .split('')
      .map(c => c + c)
      .join('');
  }

  // Parse hex to RGB
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);

  // Darken by reducing brightness (multiply by factor < 1)
  const darkerR = Math.max(0, Math.floor(r * factor));
  const darkerG = Math.max(0, Math.floor(g * factor));
  const darkerB = Math.max(0, Math.floor(b * factor));

  // Convert back to hex
  const darkerHex = ((darkerR << 16) | (darkerG << 8) | darkerB).toString(16).padStart(6, '0');
  return `#${darkerHex}`;
};

const AnimatedPressable = Animated.createAnimatedComponent(RNPressable);

export interface ButtonProps extends PressableProps {
  /**
   * Accessibility hint for screen readers
   */
  accessibilityHint?: string;
  /**
   * Accessibility label for screen readers
   */
  accessibilityLabel?: string;
  /**
   * Left icon element
   */
  leftIcon?: ReactElement;
  /**
   * Right icon element
   */
  rightIcon?: ReactElement;
  /**
   * Disabled state
   */
  isDisabled?: boolean;
  /**
   * Loading state
   */
  isLoading?: boolean;
  /**
   * Press handler
   */
  onPress: () => void;
  /**
   * Button content (text or custom component)
   */
  children?: string | ReactElement;
  /**
   * Background color enum. Default to 'purple500'.
   */
  bg?: ThemeColorKeys;
  /**
   * Text color enum
   */
  color?: ThemeColorKeys;
  /**
   * Border radius
   */
  br?: number;
  /**
   * Border width
   */
  bw?: number;
  /**
   * Border color enum
   */
  bc?: ThemeColorKeys;
  /**
   * Padding horizontal
   */
  px?: number;
  /**
   * Padding vertical
   */
  py?: number;
  /**
   * Margin Top
   */
  mt?: number;
  /**
   * Margin Bottom
   */
  mb?: number;
  /**
   * Margin horizontal
   */
  mx?: number;
  /**
   * Margin vertical
   */
  my?: number;
  /**
   * Additional styles for the button container
   */
  style?: ViewStyle;
  /**
   * If true, button takes full width of parent; if false, shrinks to content
   */
  isFullWidth?: boolean;
  /**
   * This is a helper for multiple  buttons in a row to align them horizontally and take equal space. Defaults to false.
   */
  isHorizontalAligned?: boolean;

  /**
   * Font Weight
   */
  fw?: FW;

  /**
   * Button Height
   * Only choose from selected numbers
   */
  h?: 32 | 36 | 42 | 46 | 48;

  fs?: FS;
}

export const Button = forwardRef<View, ButtonProps>(
  (
    {
      accessibilityHint,
      accessibilityLabel,
      leftIcon,
      rightIcon,
      isDisabled = false,
      isLoading = false,
      onPress,
      children,
      bg = 'purple500',
      color = 'white',
      br = 38,
      bw,
      h = 42,
      bc,
      px = 12,
      py = 8,
      mt,
      mb,
      mx,
      my,
      style,
      fw = '600',
      isFullWidth = true,
      isHorizontalAligned = false,
      fs,
      ...props
    },
    ref,
  ) => {
    const { colors } = useTheme();
    const transition = useSharedValue(0);
    const isActive = useSharedValue(false);
    const darkColor = darkenColor(colors[bg], 0.8);

    const animatedStyles = useAnimatedStyle(() => ({
      backgroundColor: interpolateColor(transition.value, [0, 1], [colors[bg], darkColor]),
    }));

    const buttonStyles: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: ms(8, 0.5),
      height: mvs(h, 0.5),
      borderRadius: ms(br, 0.5),
      ...(bw !== undefined && { borderWidth: ms(bw, 0.5) }),
      ...(bc !== undefined && { borderColor: colors[bc] }),
      paddingHorizontal: ms(px, 0.5),
      paddingVertical: mvs(py, 0.5),
      ...(mx !== undefined && { marginHorizontal: ms(mx, 0.5) }),
      ...(my !== undefined && { marginVertical: mvs(my, 0.5) }),
      ...(mt !== undefined && { marginTop: ms(mt, 0.5) }),
      ...(mb !== undefined && { marginBottom: mvs(mb, 0.5) }),
      opacity: isDisabled || isLoading ? 0.8 : 1,
      ...(isFullWidth && isHorizontalAligned && { flex: 1 }),
      alignSelf: isFullWidth ? 'stretch' : 'center',
    };

    const titleStyles: TextStyle = {
      color: colors[color],
      fontSize: ms(16, 0.5),
      fontWeight: fw ?? '600',
      flexShrink: 1,
    };

    return (
      <AnimatedPressable
        ref={ref}
        accessibilityHint={accessibilityHint}
        accessibilityLabel={accessibilityLabel}
        role="button"
        accessibilityState={{
          busy: isLoading,
          disabled: isDisabled || isLoading,
        }}
        disabled={isDisabled || isLoading}
        onPress={onPress}
        onPressIn={() => {
          isActive.value = true;
          transition.value = withTiming(1, { duration: DURATION });
        }}
        onPressOut={() => {
          isActive.value = false;
          transition.value = withTiming(0, { duration: DURATION });
        }}
        style={[buttonStyles, style, animatedStyles]}
        {...props}>
        {isLoading ? (
          <RingSpinner />
        ) : (
          <>
            {leftIcon && <View style={{ marginRight: ms(5, 0.5) }}>{leftIcon}</View>}
            {isValidElement(children) ? (
              children
            ) : (
              <Text fw={fw} fs={fs} style={titleStyles}>
                {children}
              </Text>
            )}
            {rightIcon && <View style={{ marginLeft: ms(5, 0.5) }}>{rightIcon}</View>}
          </>
        )}
      </AnimatedPressable>
    );
  },
);

Button.displayName = 'Button';
