import { ErrorResponse } from "@/lib/error-handlers-module/index.js";
import { Paginator } from "@/lib/pagination-module.js";
import { UTILS } from "@/lib/utils.js";
import db from "@repo/db";
import {
  chatRequestsTable,
  chatRequestStatusEnum,
  chatRoomTable,
  userDetails,
} from "@repo/db/schema";
import { and, count, desc, eq, or } from "drizzle-orm";
import { NextFunction, Request, Response } from "express";

export class ChatRequestsController {
  static async requestUserForChat(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const userId = req.user?.id;
      const requestedUserId = req.params.id as string;

      if (userId === requestedUserId) {
        throw new ErrorResponse("Cannot request chat with yourself", 400);
      }

      const existingRequest = await db
        .select()
        .from(chatRequestsTable)
        .where(
          and(
            eq(chatRequestsTable.userId, userId),
            eq(chatRequestsTable.requestedUserId, requestedUserId),
            eq(chatRequestsTable.status, "pending"),
          ),
        );

      if (existingRequest.length > 0) {
        throw new ErrorResponse(
          "You already requested this user for chat.",
          400,
        );
      }

      const [newRequest] = await db
        .insert(chatRequestsTable)
        .values({
          userId,
          requestedUserId,
          status: "pending",
          createdAt: new Date(),
        })
        .returning();

      // TODO : Push Notify requestUser that userId want to chat with that user(requestedUser)..

      //Check for mutual interst
      const reciprocalRequest = await db
        .select()
        .from(chatRequestsTable)
        .where(
          and(
            // Heree checking from another user's side interest.
            eq(chatRequestsTable.userId, requestedUserId),
            eq(chatRequestsTable.requestedUserId, userId),
            eq(chatRequestsTable.status, chatRequestStatusEnum.enumValues[0]), // pending
          ),
        );

      if (reciprocalRequest.length > 0) {
        // If both agree then here create/upsert a room
        // for both and notify myself to refetch the chat list and request list
        const { roomId, sortedUser1, sortedUser2 } = UTILS.getRoomId(
          userId,
          requestedUserId,
        );
        const [newRoom] = await db
          .insert(chatRoomTable)
          .values({
            user1: sortedUser1 as string,
            user2: sortedUser2 as string,
            roomId: roomId,
            createdAt: new Date(),
          })
          .returning({ id: chatRoomTable.id, roomId: chatRoomTable.roomId });

        // Update both requests to accepted
        await db
          .update(chatRequestsTable)
          .set({ status: "accepted", updatedAt: new Date() })
          .where(
            or(
              and(
                eq(chatRequestsTable.userId, userId),
                eq(chatRequestsTable.requestedUserId, requestedUserId),
              ),
              and(
                eq(chatRequestsTable.userId, requestedUserId),
                eq(chatRequestsTable.requestedUserId, userId),
              ),
            ),
          );

        res.status(201).json({
          message: "Chat Request accepted successfully",
          data: {
            data: {
              isMutualInterest: true,
              roomId: newRoom?.roomId,
            },
          },
        });
        return;
      }

      res.status(200).json({
        message: "Chat request sent successfully",
        data: {
          data: {
            isMutualInterest: false,
            newRequest,
          },
        },
      });

      return;
    } catch (error) {
      next(error);
    }
  }

  static async getPendingRequests(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      // simply use paginated query and from requestedUserId key search if it matches with your userId .
      const { limit, offset, page } = Paginator.getPage(req.query);
      const myUserId = req.user.id;

      const total = await Paginator.getTotalCount(
        db
          .select({ count: count() })
          .from(chatRequestsTable)
          .innerJoin(userDetails, eq(chatRequestsTable.userId, userDetails.id))
          .where(
            and(
              eq(chatRequestsTable.requestedUserId, myUserId),
              eq(chatRequestsTable.status, "pending"),
            ),
          ),
      );

      const rows = await db
        .select({
          id: chatRequestsTable.id,
          status: chatRequestsTable.status,
          user: {
            id: userDetails.userId,
            avatar: userDetails.avatarUrl,
            username: userDetails.userName,
          },
        })
        .from(chatRequestsTable)
        .innerJoin(userDetails, eq(chatRequestsTable.userId, userDetails.id))
        .where(
          and(
            eq(chatRequestsTable.requestedUserId, myUserId),
            eq(chatRequestsTable.status, "pending"),
          ),
        )
        .orderBy(desc(chatRequestsTable.createdAt))
        .limit(limit)
        .offset(offset);

      const data = Paginator.paginate(req.query, rows, total);
      res.json({ data });
    } catch (error) {
      next(error);
    }
  }

  static async acceptRequest(req: Request, res: Response, next: NextFunction) {
    try {
      // check if request exist in pending state -> if exists then mark it accepted ,
      // then create a conversation thread with roomId and both user ids with notifying another user
      // with push to refetch chat and requests apis.
    } catch (error) {
      next(error);
    }
  }

  static async rejectRequest(req: Request, res: Response, next: NextFunction) {
    try {
      // If user clicks on this then simply delete that request from the requests table and
      // remove that request from my chat requests section optimistic wiht removal on frontend side as well.
    } catch (error) {
      next(error);
    }
  }
}
