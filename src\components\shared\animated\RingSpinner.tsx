import { ThemeColorKeys } from '@/types/color-types';
import { View as CView } from '@components/native';
import React, { useEffect, useRef } from 'react';
import { Animated, View, ViewStyle } from 'react-native';
export interface RingSpinnerProps {
  /**
   * Default to 24
   */
  size?: number;
  strokeWidth?: number;
  color?: ThemeColorKeys;
}

export const RingSpinner: React.FC<RingSpinnerProps> = ({ size = 24, strokeWidth = 2, color = 'white' }) => {
  const rotationDegree = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.timing(rotationDegree, {
        toValue: 360,
        duration: 800,
        easing: t => t, // Linear easing for constant speed
        useNativeDriver: true,
      }),
    );
    animation.start();
    return () => animation.stop();
  }, [rotationDegree]);

  const interpolatedRotation = rotationDegree.interpolate({
    inputRange: [0, 360],
    outputRange: ['0deg', '360deg'],
  });

  const containerStyle: ViewStyle = {
    width: size,
    height: size,
    alignItems: 'center',
    justifyContent: 'center',
  };

  const baseStyle: ViewStyle = {
    width: size,
    height: size,
    borderWidth: strokeWidth,
    borderRadius: size / 2,
    position: 'absolute',
  };

  const backgroundStyle: ViewStyle = {
    ...baseStyle,
    borderColor: color,
    opacity: 0.25, // Translucent background
  };

  const progressStyle: ViewStyle = {
    ...baseStyle,
    borderTopColor: color, // Opaque quarter
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
    borderLeftColor: 'transparent',
    transform: [{ rotateZ: interpolatedRotation }],
  };

  return (
    <View style={containerStyle} accessibilityRole="progressbar">
      <View style={backgroundStyle} />
      <Animated.View style={progressStyle} />
    </View>
  );
};

export const LoaderCentered = () => {
  return (
    <CView flex={1} flexCenterRow>
      <RingSpinner color="purple500" size={30} />
    </CView>
  );
};
