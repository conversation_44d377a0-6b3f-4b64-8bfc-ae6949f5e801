import HandWave from '@assets/svgs/hand-wavesvg.svg';
import { Text, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { MotiView } from 'moti';
import { Easing } from 'react-native-reanimated';

export const ChatScreen = () => {
  return (
    <ScreenWrapper hideBackButton={false} title="@sunshine">
      <View flex={1} gap={40} display="flex" fd="column" ai="center" mt={50}>
        <Text fw="500" color="neutral80">
          Say Hi! and start a conversation.
        </Text>
        <MotiView
          from={{
            rotate: '0deg', // Starting rotation
            translateY: 0, // Starting position
          }}
          animate={{
            rotate: ['0deg', '35deg', '0deg', '-15deg', '0deg'], // Rotate left, then right, mimicking wave
            translateY: [0, 5, 0, 5, 0],
          }}
          transition={{
            type: 'timing',
            duration: 200, // Duration of each segment
            // loop: true, // Repeat indefinitely
            repeatReverse: false, // Cycle through the sequence
            easing: Easing.linear, // Smooth transitions
          }}>
          <HandWave />
        </MotiView>
      </View>
    </ScreenWrapper>
  );
};
