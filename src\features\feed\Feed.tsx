import { ThemeColors } from '@/types/color-types';
import CommentIcon from '@assets/svgs/comment-icon.svg';
import CreateButtonContainer from '@assets/svgs/create-feed-button-container.svg';
import HashtagIcon from '@assets/svgs/hashtag-icon.svg';
import HeartIconFilled from '@assets/svgs/heart-icon-filled.svg';
import HeartIcon from '@assets/svgs/heart-icon.svg';
import PlusIcon from '@assets/svgs/plus-icon.svg';
import PollIcon from '@assets/svgs/poll-icon.svg';
import SaveIconFilled from '@assets/svgs/save-icon-filled.svg';
import SaveIcon from '@assets/svgs/save-icon.svg';
import { Button, Pressable, Text, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { BounceTap } from '@components/shared/animated';
import { useBottomSheet } from '@components/shared/bottom-sheet/BottomSheet';
import { useTheme } from '@context/index';
import { BottomSheetFlashList } from '@gorhom/bottom-sheet';
import { router } from '@navigation/refs/navigation-ref';
import { FlashList } from '@shopify/flash-list';
import SCREENS from '@src/SCREENS';
import React, { useEffect, useState } from 'react';
import { Image } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import Feather from 'react-native-vector-icons/Feather';

interface Comment {
  username: string;
  avatar: any;
  timeAgo: string;
  content: string;
  replies: CommentReplies[];
}

interface CommentReplies {
  username: string;
  avatar: any;
  timeAgo: string;
  content: string;
  repliedTo: string;
}

interface Post {
  username: string;
  avatar: any;
  timeAgo: string;
  saved: boolean;
  liked: boolean;
  likeCount: number;
  postTitle: string;
  postContent: string;
  hashtags?: string[];
  comments: Comment[];
}

const MOCK_POSTS: Post[] = [
  {
    username: 'travel_guru',
    avatar: require('@assets/placeholders/feed-avatar-1.png'),
    timeAgo: '10 mins ago',
    saved: true,
    liked: true,
    likeCount: 120,
    postTitle: 'Exploring the Alps!',
    postContent: 'Just finished hiking the beautiful Alps. The view from the top is breathtaking! Highly recommend to all adventure lovers.',
    hashtags: ['#Alps', '#Adventure', '#Travel'],
    comments: [
      {
        username: 'mountain_lover',
        avatar: require('@assets/placeholders/comment-avatar-1.png'),
        timeAgo: '5 mins ago',
        content: 'Wow, looks amazing! How long was the hike?',
        replies: [
          {
            username: 'travel_guru',
            avatar: require('@assets/placeholders/feed-avatar-1.png'),
            timeAgo: '3 mins ago',
            content: 'It took around 5 hours. The trail was steep but worth every step!',
            repliedTo: 'mountain_lover',
          },
          {
            username: 'mountain_lover',
            avatar: require('@assets/placeholders/comment-avatar-1.png'),
            timeAgo: '2 mins ago',
            content: 'That sounds exhausting! But definitely worth it. What was the weather like?',
            repliedTo: 'travel_guru',
          },
          {
            username: 'travel_guru',
            avatar: require('@assets/placeholders/feed-avatar-1.png'),
            timeAgo: '1 min ago',
            content: 'The weather was perfect. Sunny and clear. The only challenge was the altitude. But it was worth it!',
            repliedTo: 'mountain_lover',
          },
        ],
      },
      {
        username: 'nature_fan',
        avatar: require('@assets/placeholders/comment-avatar-2.png'),
        timeAgo: '3 mins ago',
        content: 'The Alps are on my bucket list! Thanks for sharing.',
        replies: [],
      },
    ],
  },
  {
    username: 'foodie_explorer',
    avatar: require('@assets/placeholders/feed-avatar-2.png'),
    timeAgo: '1 hour ago',
    saved: false,
    liked: false,
    likeCount: 89,
    postTitle: 'Best Pizza in Naples',
    postContent: 'Tried the most delicious pizza in Naples today. The crust was perfect and the cheese was so fresh!',
    hashtags: ['#Foodie', '#Pizza', '#Naples'],
    comments: [
      {
        username: 'pizza_lover',
        avatar: require('@assets/placeholders/comment-avatar-3.png'),
        timeAgo: '45 mins ago',
        content: 'I need to try this place! Where exactly is it?',
        replies: [],
      },
    ],
  },
  {
    username: 'wellness_journey',
    avatar: require('@assets/placeholders/feed-avatar-3.png'),
    timeAgo: '2 days ago',
    saved: false,
    liked: false,
    likeCount: 200,
    postTitle: 'Morning Yoga Routine',
    postContent: 'Started a new morning yoga routine and it has changed my life. Feeling more energetic and focused every day.',
    hashtags: ['#Yoga', '#Wellness', '#Routine'],
    comments: [
      {
        username: 'yogi_life',
        avatar: require('@assets/placeholders/comment-avatar-4.png'),
        timeAgo: '1 day ago',
        content: 'So inspiring! Can you share your routine?',
        replies: [],
      },
      {
        username: 'fit_mom',
        avatar: require('@assets/placeholders/comment-avatar-2.png'),
        timeAgo: '1 day ago',
        content: 'Love this! I need to get back to yoga.',
        replies: [],
      },
    ],
  },
];

interface Poll {
  username: string;
  avatar: any;
  timeAgo: string;
  liked: boolean;
  likeCount: number;
  question: string;
  options: string[];
  votes: number[];
  voted: number | undefined;
  comments: Comment[];
}

const MOCK_POLLS: Poll[] = [
  {
    username: 'poll_creator1',
    avatar: require('@assets/placeholders/feed-avatar-1.png'),
    timeAgo: '1 hour ago',
    liked: false,
    likeCount: 101,
    question: 'Which mobile operating system do you prefer?',
    options: ['iOS', 'Android'],
    votes: [30, 27],
    voted: undefined,
    comments: [
      {
        username: 'tech_enthusiast',
        avatar: require('@assets/placeholders/comment-avatar-1.png'),
        timeAgo: '45 mins ago',
        content: 'iOS for its seamless ecosystem and privacy features!',
        replies: [],
      },
      {
        username: 'android_dev',
        avatar: require('@assets/placeholders/comment-avatar-3.png'),
        timeAgo: '30 mins ago',
        content: 'Android offers more customization and flexibility. Plus, the variety of devices is amazing!',
        replies: [],
      },
      {
        username: 'mobile_expert',
        avatar: require('@assets/placeholders/comment-avatar-4.png'),
        timeAgo: '15 mins ago',
        content: 'Both have their strengths. iOS for simplicity, Android for versatility.',
        replies: [],
      },
    ],
  },
  {
    username: 'poll_creator2',
    avatar: require('@assets/placeholders/feed-avatar-2.png'),
    timeAgo: '2 hours ago',
    liked: false,
    likeCount: 77,
    question: 'What is your favorite food?',
    options: ['Pizza', 'Burger', 'Sushi', 'Pasta'],
    votes: [15, 10, 20, 5],
    voted: undefined,
    comments: [
      {
        username: 'foodie_explorer',
        avatar: require('@assets/placeholders/comment-avatar-2.png'),
        timeAgo: '1 hour ago',
        content: 'Pizza is the clear winner!',
        replies: [],
      },
    ],
  },
];

const CardHeader = React.memo(({ item, colors }: { item: Post | Poll; colors: ThemeColors }) => {
  return (
    <View>
      <View fd="row" ai="center" mb={16} jc="space-between">
        <View fd="row" ai="center">
          <Image width={40} height={40} borderRadius={20} source={item.avatar} style={{ marginRight: 12, borderWidth: 1.6, borderColor: colors.neutral10 }} />
          <View>
            <Text
              // ff="PoppinsRegular"
              fs="16"
              fw="700">
              @{item.username}
            </Text>
            <Text
              // ff="PoppinsRegular"
              fs="12"
              color="neutral60"
              mt={4}>
              {item.timeAgo}
            </Text>
          </View>
        </View>
        <Pressable onPress={() => {}}>
          <Feather name="more-vertical" size={18} color={colors.foreground} />
        </Pressable>
      </View>

      {'postTitle' in item && (
        <Text
          // ff="PoppinsRegular"
          fw="700"
          fs="18">
          {item.postTitle}
        </Text>
      )}

      {'question' in item && (
        <View fd="row" fw="wrap" ai="center">
          <Text
            // ff="PoppinsRegular"
            fw="700"
            fs="18">
            {item.question}
          </Text>
          {/* <View bg="lightBlue" m={5} px={6} py={3} br={6}>
            <Text
              // ff="PoppinsRegular"
              fw="400"
              fs="12">
              poll
            </Text>
          </View> */}
        </View>
      )}
    </View>
  );
});

const CardFooter = ({ item, onOpenSheet }: { item: Post | Poll; onOpenSheet: (comments: Comment[]) => void }) => {
  const [liked, setLiked] = useState(item.liked);
  const [saved, setSaved] = useState('saved' in item ? item.saved : false);

  return (
    <View>
      <View mt={16} fd="row" ai="center" jc="space-between">
        <View fd="row" ai="center">
          <Pressable onPress={() => setLiked(!liked)}>
            {liked ? <HeartIconFilled width={24} height={24} style={{ marginRight: 6 }} /> : <HeartIcon width={24} height={24} style={{ marginRight: 6 }} />}
          </Pressable>
          <Text mr={16} fs="14">
            {item.likeCount}
          </Text>
          <Pressable onPress={() => onOpenSheet(item.comments)} style={{ flexDirection: 'row', alignItems: 'center' }}>
            <CommentIcon width={24} height={24} style={{ marginRight: 6 }} />
            <Text fs="14">{item.comments.length}</Text>
          </Pressable>
        </View>
        {'saved' in item && (
          <View>
            <Pressable onPress={() => setSaved(!saved)}>{saved ? <SaveIconFilled width={24} height={24} /> : <SaveIcon width={24} height={24} />}</Pressable>
          </View>
        )}
      </View>
    </View>
  );
};

const PostCard = React.memo(({ post, onOpenSheet }: { post: Post; onOpenSheet: (comments: Comment[]) => void }) => {
  const { colors } = useTheme();
  return (
    <View px={12} pb={24}>
      <View bg="white" br={20} bw={1} bc="purpleLight">
        <View px={13} py={16}>
          <CardHeader item={post} colors={colors} />
          <Text
            // ff="PoppinsRegular"
            mt={8}>
            {post.postContent}
          </Text>

          {post.hashtags && post.hashtags.length > 0 && (
            <Text
              // ff="PoppinsRegular"
              color="purple500"
              fw="600"
              fs="14">
              {post.hashtags.join(' ')}
            </Text>
          )}
          <CardFooter item={post} onOpenSheet={onOpenSheet} />
        </View>
      </View>
    </View>
  );
});

const PollCard = React.memo(({ poll, onOpenSheet }: { poll: Poll; onOpenSheet: (comments: Comment[]) => void }) => {
  const { colors } = useTheme();
  const [voted, setVoted] = useState<number | undefined>(poll.voted);
  const [votes, setVotes] = useState<number[]>(poll.votes);
  const widthAnimations = poll.options.map(() => useSharedValue(0));
  const springConfig = { damping: 100, stiffness: 150, mass: 1 };

  // Initialize animation values when component mounts or when poll already has votes
  useEffect(() => {
    if (voted !== undefined) {
      const currentTotalVotes = votes.reduce((a, b) => a + b, 0);
      votes.forEach((voteCount, i) => {
        const percentage = Math.round((voteCount / currentTotalVotes) * 100);
        widthAnimations[i].value = withSpring(percentage, springConfig);
      });
    }
  }, [voted, votes, widthAnimations]);

  const handleVote = (index: number) => {
    if (voted !== undefined) return;
    const newVotes = [...votes];
    newVotes[index] += 1;
    setVotes(newVotes);
    setVoted(index);

    // Animate all poll bars when voting
    const newTotalVotes = newVotes.reduce((a, b) => a + b, 0);
    newVotes.forEach((voteCount, i) => {
      const percentage = Math.round((voteCount / newTotalVotes) * 100);
      widthAnimations[i].value = withSpring(percentage, springConfig);
    });
  };

  const totalVotes = votes.reduce((a, b) => a + b, 0);
  const getVotePercentage = (index: number) => Math.round((votes[index] / totalVotes) * 100);

  // Create animated styles for each poll option
  const createAnimatedBarStyle = (index: number) =>
    useAnimatedStyle(() => ({
      width: `${widthAnimations[index].value}%`,
    }));

  return (
    <View px={8} pb={24}>
      <View bg="white" br={20} bw={1} bc="purpleLight">
        <View px={13} py={16}>
          <CardHeader item={poll} colors={colors} />
          {poll.options.map((option, index) => {
            const animatedBarStyle = createAnimatedBarStyle(index);
            return (
              <Pressable key={index} onPress={() => handleVote(index)} disabled={voted !== undefined}>
                <View bg="purple100" jc="center" h={48} br={10} mt={12}>
                  <Animated.View
                    style={[
                      {
                        backgroundColor: colors.purple300,
                        height: '100%',
                        borderRadius: 10,
                        position: 'absolute',
                        left: 0,
                      },
                      animatedBarStyle,
                    ]}
                  />
                  <View fd="row" ai="center" jc="space-between" px={21}>
                    <Text key={'option-' + index} fw="500" fs="16">
                      {option}
                    </Text>
                    {voted !== undefined && (
                      <View fd="row" ai="center">
                        {voted === index && <Image source={require('@assets/placeholders/avatar.png')} style={{ width: 18, height: 18, borderRadius: 30, marginRight: 9 }} />}
                        <Text fw="700" fs="16" color="neutral80">
                          {getVotePercentage(index)}%
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              </Pressable>
            );
          })}
          <View h={23}>
            {voted !== undefined && (
              <View fd="row" ai="center" jc="space-between" pos="absolute" style={{ bottom: 0, left: 0, right: 0 }}>
                <Text fw="500" fs="12" color="neutral40">
                  {totalVotes} responses
                </Text>
                <Pressable
                  onPress={() => {
                    if (voted !== undefined) {
                      const newVotes = [...votes];
                      newVotes[voted] -= 1;
                      setVotes(newVotes);
                      setVoted(undefined);

                      // Animate all bars to 0 when removing vote
                      widthAnimations.forEach(animation => {
                        animation.value = withSpring(0, springConfig);
                      });
                    }
                  }}>
                  <Text fw="600" fs="12" color="neutral50">
                    Remove vote?
                  </Text>
                </Pressable>
              </View>
            )}
          </View>
          <CardFooter item={poll} onOpenSheet={onOpenSheet} />
        </View>
      </View>
    </View>
  );
});

const CreateButton = React.memo(() => {
  const { colors } = useTheme();
  const [showView, setShowView] = useState(false);
  return (
    <View pos="absolute" bottom={60} z={20} right={24}>
      <View bottom={-4} z={20} right={-4.5}>
        {showView && (
          <View>
            <CreateButtonContainer color={colors.background} />
            <View pos="absolute" px={10}>
              <Pressable onPress={() => router.navigate(SCREENS.CREATE_NEW_POST)}>
                <View fd="row" py={10}>
                  <HashtagIcon width={24} height={24} style={{ marginRight: 6 }} />
                  <Text fw="500" fs="14" color="purple500">
                    Add post
                  </Text>
                </View>
              </Pressable>
              <View h={1} w={'100%'} bg="neutral20" />
              <Pressable onPress={() => router.navigate(SCREENS.CREATE_NEW_POLL)}>
                <View fd="row" py={10}>
                  <PollIcon width={24} height={24} style={{ marginRight: 6 }} />
                  <Text fw="500" fs="14" color="purple500">
                    Add poll
                  </Text>
                </View>
              </Pressable>
            </View>
          </View>
        )}
      </View>
      <View pos="absolute" bottom={0} z={20} right={0}>
        <BounceTap onPress={() => setShowView(!showView)}>
          <View size={50} flexCenterRow bg={showView ? 'neutral30' : 'purple500'} br={100} bw={1} bc="purpleLight">
            <PlusIcon fill={colors.background} style={{ transform: [{ rotate: showView ? '45deg' : '0deg' }] }} />
          </View>
        </BounceTap>
      </View>
    </View>
  );
});

const CommentSheet = ({ comments, Sheet, closeSheet }: { comments: Comment[]; Sheet: React.FC<any>; closeSheet: () => void }) => {
  const [expandedReplies, setExpandedReplies] = useState<number[]>([]);

  const handleToggleReplies = React.useCallback((index: number) => {
    setExpandedReplies(prev => {
      if (prev.includes(index)) {
        console.log('hiding replies for:', index);
        return prev.filter(i => i !== index);
      } else {
        console.log('showing replies for:', index);
        return [...prev, index];
      }
    });
  }, []);

  const renderCommentItem = React.useCallback(
    ({ item: comment, index }: { item: Comment; index: number }) => {
      const showReplies = expandedReplies.includes(index);
      return (
        <View key={'comment-' + index} mb={18}>
          <View fd="row" ai="center" mb={0}>
            <Image width={36} height={36} borderRadius={18} source={comment.avatar} />
            <View w={6} />
            <Text fw="400" fs="14" color="neutral80">
              {comment.username}
            </Text>
            <View flex={1} />
            <Text color="neutral50" fs="10">
              {comment.timeAgo}
            </Text>
          </View>
          <View fd="row" ai="flex-start">
            <View w={36} />
            <View flex={1}>
              <Text fs="10" color="neutral70" mt={2}>
                {comment.content}
              </Text>
              <View h={8} />
              <View fd="row" ai="center">
                <Text ff="Montserrat-Regular" fs="12" color="neutral70" fw="700">
                  Reply
                </Text>
                {comment.replies.length > 0 && (
                  <>
                    <View w={2} h={2} mx={5} bg="neutral80" br={100} />
                    <Pressable onPress={() => handleToggleReplies(index)}>
                      {showReplies ? (
                        <Text fw="400" fs="10" color="neutral50">
                          Hide {comment.replies.length === 1 ? 'reply' : 'replies'}
                        </Text>
                      ) : (
                        <Text fw="400" fs="10" color="neutral50">
                          Show {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}
                        </Text>
                      )}
                    </Pressable>
                  </>
                )}
              </View>
              {showReplies && (
                <View fd="row">
                  <View w={3} bg="purpleLight" />
                  <BottomSheetFlashList
                    data={comment.replies}
                    estimatedItemSize={30}
                    contentContainerStyle={{ paddingTop: 8, paddingLeft: 8, backgroundColor: 'background' }}
                    renderItem={({ item: reply, index: rIndex }) => (
                      <View key={'reply-' + rIndex} mb={8}>
                        <View fd="row" ai="center" mb={0}>
                          <Image width={36} height={36} borderRadius={18} source={reply.avatar} />
                          <View w={6} />
                          <Text fw="400" fs="14" color="neutral80">
                            {reply.username}
                          </Text>
                          <View flex={1} />
                          <Text color="neutral50" fs="10">
                            {reply.timeAgo}
                          </Text>
                        </View>
                        <Text fs="10" color="neutral70" fw="700">
                          {reply.repliedTo}
                        </Text>
                        <View fd="row" ai="flex-start">
                          <View flex={1}>
                            <Text fs="10" color="neutral70" mt={2}>
                              {reply.content}
                            </Text>
                          </View>
                        </View>
                        <View h={8} />
                        <View fd="row" ai="center">
                          <Text fs="12" color="neutral70" fw="700">
                            Reply
                          </Text>
                        </View>
                      </View>
                    )}
                  />
                  {/* </View> */}
                </View>
              )}
            </View>
          </View>
        </View>
      );
    },
    [expandedReplies, handleToggleReplies],
  );

  return (
    <Sheet withBackButton={false} enablePanDownToClose={true} onDismiss={closeSheet}>
      <BottomSheetFlashList
        data={comments}
        estimatedItemSize={30}
        ListHeaderComponent={
          <View pt={25} pb={10}>
            <Text fs="24" fw="600" ta="left" color="neutral80">
              Comments
            </Text>
          </View>
        }
        contentContainerStyle={{ paddingHorizontal: 20 }}
        renderItem={renderCommentItem}
        extraData={expandedReplies}
      />
    </Sheet>
  );
};

export const FeedScreen = () => {
  const profileCompletion = 60;
  const feedData = React.useMemo(() => [...MOCK_POSTS, ...MOCK_POLLS], []);

  const { Sheet, openSheet, closeSheet } = useBottomSheet({ snapPoints: ['100%'] });
  const [comments, setComments] = useState<Comment[]>([]);

  const onOpenSheet = React.useCallback((comments: Comment[]) => {
    setComments(comments);
  }, []);

  React.useEffect(() => {
    if (comments.length > 0) {
      openSheet();
    }
  }, [comments]);

  return (
    <ScreenWrapper hideHeader>
      <CreateButton />
      <FlashList
        ListFooterComponent={() => <View h={15} />}
        ListHeaderComponent={
          <View px={12} pb={24}>
            {profileCompletion < 100 && (
              <View mt={11}>
                <View mih={200} bg="lightBlue" br={20} bw={1} bc="purpleLight" px={13} pb={13} pt={23}>
                  <View fd="row" ai="flex-start">
                    <View>
                      <Text ff="PlayfairDisplay-Regular" fw="700" fs="20" color="purple700">
                        Let's Personalize{'\n'}Your Journey 💫
                      </Text>
                      <Text mt={19} fw="400" fs="12" color="neutral60">
                        A few more details will help us find{'\n'}people who truly get you.
                      </Text>
                    </View>
                  </View>
                  <View fd="row" jc="space-between" mt={15} mb={25}>
                    <View flex={1} bg="background" br={20} bw={1} bc="neutral10" h={10}>
                      <View flex={1} w={`${profileCompletion}%`} bg="purple300" br={20} />
                    </View>
                    <Text
                      // ff='PoppinsRegular'
                      fw="400"
                      fs="10"
                      color="neutral70"
                      ml={9}>
                      {profileCompletion}% complete, Almost there...
                    </Text>
                  </View>
                  <Button isFullWidth={true} onPress={() => {}}>
                    Update profile
                  </Button>
                </View>
                <View pos="absolute" top={-30} right={0}>
                  <Image source={require('@assets/images/complete-profile.png')} />
                </View>
              </View>
            )}
          </View>
        }
        data={feedData}
        renderItem={({ item }: { item: Post | Poll }) => {
          if ('postTitle' in item) {
            return <PostCard post={item} onOpenSheet={onOpenSheet} />;
          }
          return <PollCard poll={item} onOpenSheet={onOpenSheet} />;
        }}
      />
      <CommentSheet
        comments={comments}
        Sheet={Sheet}
        closeSheet={() => {
          closeSheet();
          setComments([]);
        }}
      />
    </ScreenWrapper>
  );
};
