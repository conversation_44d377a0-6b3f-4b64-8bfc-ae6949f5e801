import CalenderIcon from '@assets/svgs/calender-icon.svg';
import { Button, Text, View } from '@components/native';
import { DatePicker, ScreenWrapper, StackBackButton } from '@components/shared';
import { notify, useAuth } from '@context/index';
import { CountryListTextInput } from '@features/public';
import useStitch from '@packages/useStitch';
import { CountryItem } from '@packages/useStitch/types/public-api-types';
import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { BackHandler, Dimensions, SafeAreaView } from 'react-native';
import UsernameInput from './component/UsernameInput';

const { width } = Dimensions.get('screen');

export const PostAuthAddPersonalDetails = () => {
  const { refetchUserInfo } = useAuth();
  const navigation = useNavigation();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [country, setCountry] = useState<CountryItem | null>(null);
  const { isMutating, mutateAsync } = useStitch('completeSignup', {
    mutationOptions: {
      onSuccess: refetchUserInfo,
    },
  });
  const [usernameObj, setUsernameObj] = useState({
    validUsername: false,
    username: '',
  });
  const dob = `${selectedDate?.getMonth()}-${selectedDate?.getDay()}-${selectedDate?.getFullYear()}`;

  // Prevent back navigation (Android and iOS)
  useEffect(() => {
    // Android: Handle hardware back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Return true to prevent default back navigation
      return true;
    });

    // iOS: Disable swipe-back gesture and intercept navigation
    navigation.setOptions({
      gestureEnabled: false, // Disable iOS swipe-back gesture
    });

    const unsubscribe = navigation.addListener('beforeRemove', e => {
      // Prevent programmatic back navigation (e.g., via router.back())
      e.preventDefault();
    });

    // Cleanup on unmount
    return () => {
      backHandler.remove();
      unsubscribe();
    };
  }, [navigation]);

  const onSubmit = async () => {
    const { validUsername, username } = usernameObj;
    if (username == '') {
      notify.top('Username cannot be empty');
      return;
    }
    if (!validUsername && username != '') {
      notify.top('Username is not valid');
      return;
    }
    if (!selectedDate) {
      notify.top('Please enter your date of birth');
      return;
    }
    if (!country) {
      notify.top('Please select your country');
      return;
    }

    // Format date to YYYY-MM-DD
    const formatDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // Add 1 to month and pad with 0
      const day = String(date.getDate()).padStart(2, '0'); // Use getDate() for day of month
      return `${year}-${month}-${day}`;
    };

    const payload = {
      username,
      country,
      ...(selectedDate && { dob: formatDate(selectedDate) }), // Include dob only if selectedDate exists
    };
    console.log(payload);
    await mutateAsync(payload);
  };

  return (
    <ScreenWrapper hideHeader>
      <View flex={1} p={20}>
        <StackBackButton hideBackArrow title="Get Started" />
        <SafeAreaView style={{ flex: 1 }}>
          <View pt={80} flex={1}>
            <UsernameInput onSuccess={(username, isValid) => setUsernameObj({ username, validUsername: isValid })} initialValue="" />
            <DatePicker onDateSelected={setSelectedDate}>
              <View bg="neutral10" px={16} br={14} py={16}>
                <View display="flex" fd="row" jc="space-between" ai="center">
                  <Text fs="12" fw="500" color={dob.match('undefined') ? 'neutral70' : 'neutral80'}>
                    {dob.match('undefined') ? 'Select DOB' : selectedDate?.toLocaleDateString()}
                  </Text>
                  <CalenderIcon />
                </View>
              </View>
            </DatePicker>
            <CountryListTextInput initialValue={country?.name} onSelect={country => setCountry(country)} />
          </View>
          <View w={width - 44} disableSizeMatter pos="absolute" bottom={20}>
            <Button h={48} isLoading={isMutating} mt={30} onPress={onSubmit}>
              START HEALING
            </Button>
          </View>
        </SafeAreaView>
      </View>
    </ScreenWrapper>
  );
};
