import { Button, Text, TextInput, View } from '@components/native';
import { Avatar, ScreenWrapper } from '@components/shared';
import { Switch } from '@components/shared/animated';
import { useAuth, useTheme } from '@context/index';
import { Controller, useForm } from 'react-hook-form';
import { Image, ScrollView } from 'react-native';

export const CreatePost = () => {
  const { colors } = useTheme();
  const { session } = useAuth();
  const { control, setValue } = useForm({
    defaultValues: {
      title: '',
      description: '',
      disableComments: false,
    },
  });
  const username = session!.user!.username;

  return (
    <ScreenWrapper
      title={
        <Text ff="PlayfairDisplay-SemiBold" fs="20" fw="600">
          New Post
        </Text>
      }>
      <ScrollView>
        <View px={20} pt={26} flex={1}>
          <View fd="row" ai="center" mb={31}>
            <Avatar showProgressRing={false} size={50} borderWidth={1} />
            <Text ml={11} fw="600" fs="14" color="neutral80">
              @{username}
            </Text>
          </View>
          {/* Add minLength 10 */}
          <TextInput label="Add Title" labelType="background" maxLength={50} control={control} name="title" gapBottom={33} />
          {/* Add minLength 30 */}
          <TextInput label="Add Description" labelType="background" maxLength={1000} control={control} name="description" multiline={true} />
          <View fd="row" ai="center" jc="space-between" pt={25}>
            <Text fw="400" fs="12" color="neutral70">
              Disable Comments
            </Text>
            <Controller
              control={control}
              name="disableComments"
              render={({ field: { value, onChange } }) => (
                <Switch
                  isActive={value}
                  onToggle={newValue => {
                    onChange(newValue);
                    setValue('disableComments', newValue);
                  }}
                />
              )}
            />
          </View>
        </View>
      </ScrollView>
      <View pos="absolute" bottom={53} left={0} right={0} px={20}>
        <Button isFullWidth={true} onPress={() => {}}>
          POST
        </Button>
      </View>
    </ScreenWrapper>
  );
};
