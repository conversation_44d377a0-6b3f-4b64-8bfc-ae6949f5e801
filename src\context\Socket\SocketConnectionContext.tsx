import { NavigationState } from '@react-navigation/native';
import SCREENS from '@src/SCREENS';
import { createContext, useContext, useEffect, useState } from 'react';

type SocketConnnectionContext = {
  onNavigationStateChange: (state: Readonly<NavigationState> | undefined) => void;
};

const SocketConnnectionContext = createContext<SocketConnnectionContext | undefined>(undefined);

export const SocketConnnectionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentRoute, setCurrentRoute] = useState<string | null>(null);

  const onNavigationStateChange = (state: Readonly<NavigationState> | undefined) => {
    if (!state) return;
    const currentRoute = state.routes[state.index];
    if (currentRoute.state?.type == 'drawer') {
      const drawerState = currentRoute.state.routes[currentRoute.state.index!];
      if (drawerState.name != 'tab') return;
      const tabState = drawerState.state?.routes[drawerState.state.index!];
      setCurrentRoute(tabState?.name ?? null);
    } else {
      setCurrentRoute(currentRoute.name);
    }
  };

  useEffect(() => {
    if (currentRoute == SCREENS.CHAT || currentRoute == SCREENS.CONVERSATIONS) {
      console.log('Connected to socket');
    } else {
      console.log('Disconncted to socket');
    }
  }, [currentRoute]);

  return (
    <SocketConnnectionContext.Provider
      value={{
        onNavigationStateChange,
      }}>
      {children}
    </SocketConnnectionContext.Provider>
  );
};

export const useSocket = () => {
  const context = useContext(SocketConnnectionContext);
  if (!context) {
    throw new Error('useSocket must be used within a ThemeProvider');
  }
  return context;
};
