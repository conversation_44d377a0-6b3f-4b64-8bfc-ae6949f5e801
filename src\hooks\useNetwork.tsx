import { useAuth } from '@context/index';
import NetInfo from '@react-native-community/netinfo';
import { createContext, useContext, useEffect, useMemo, useState } from 'react';

// Define the shape of the context
interface NetworkContextType {
  isOnline: boolean;
}

// Create the Network Context
const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

/**
 * Provider component to manage network connectivity status for the entire app.
 * Wrap your app with this provider to enable network status monitoring.
 * @param children - The child components that will have access to the network status.
 * @example
 * ```typescript
 * import { NetworkProvider } from './NetworkContext';
 *
 * const App = () => (
 *   <NetworkProvider>
 *     <YourApp />
 *   </NetworkProvider>
 * );
 * ```
 */
export const NetworkProvider = ({ children }: { children: React.ReactNode }) => {
  const [isOnline, setIsOnline] = useState<boolean>(false);
  const { autoLogin } = useAuth();

  useEffect(() => {
    let wasPreviouslyOffline = false;

    // Subscribe to network state updates
    const unsubscribe = NetInfo.addEventListener(state => {
      const isConnected = state.isConnected ?? false;
      setIsOnline(isConnected);

      // Log connectivity changes
      if (isConnected) {
        console.log('Device is online');
        // Call refetchUserInfo when transitioning from offline to online
        if (wasPreviouslyOffline) {
          autoLogin?.();
        }
      } else {
        console.log('Device is offline');
      }
      wasPreviouslyOffline = !isConnected;
    });

    // Fetch initial network state
    NetInfo.fetch().then(state => {
      const isConnected = state.isConnected ?? false;
      setIsOnline(isConnected);
      wasPreviouslyOffline = !isConnected;
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [autoLogin]);

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({ isOnline }), [isOnline]);

  return <NetworkContext.Provider value={value}>{children}</NetworkContext.Provider>;
};
/**
 * Hook to access the current network connectivity status.
 * Must be used within a NetworkProvider.
 * @returns An object containing `isOnline` boolean indicating if the device is connected to the internet.
 * @throws Error if used outside of a NetworkProvider.
 * @example
 * ```typescript
 * import { useNetwork } from './NetworkContext';
 *
 * const MyComponent = () => {
 *   const { isOnline } = useNetwork();
 *   return <Text>{isOnline ? 'Online' : 'Offline'}</Text>;
 * };
 * ```
 */
export const useNetwork = (): NetworkContextType => {
  const context = useContext(NetworkContext);
  if (context === undefined) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  // Return memoized context value
  return useMemo(() => context, [context]);
};
