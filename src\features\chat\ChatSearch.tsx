import SearchIcon from '@assets/svgs/search-icon.svg';
import { TextInput, View } from '@components/native';
import { StackBackButton } from '@components/shared';
import { LoaderCentered } from '@components/shared/animated';
import { useTheme } from '@context/index';
import React from 'react';
import { useForm } from 'react-hook-form';
import { Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const ChatHeader = () => {
  const { control, handleSubmit, setValue } = useForm({
    defaultValues: {
      search: '',
    },
  });
  const { colors } = useTheme();

  return (
    <View display="flex" gap={10} ai="flex-start" fd="row" px={20}>
      <View style={shadow}>
        <StackBackButton bg="background" bc="purpleLight" withAbsolute={false} />
      </View>
      <View flex={1} bw={1} bc="purpleLight" br={16} style={shadow}>
        <TextInput
          inputPaddingTop={14}
          inputPaddingBottom={4}
          autoFocus
          focusedBc="transparent"
          bg="background"
          control={control}
          name="search"
          gapBottom={0}
          label="Search"
          suffixIconAlwaysVisible
          suffixIcon={<SearchIcon stroke={colors.purple500} style={{ top: -3 }} />}
          labelType="background"
        />
      </View>
    </View>
  );
};

export const ChatSearch = () => {
  const { top } = useSafeAreaInsets();
  return (
    <View pt={top + 5} bg="background" flex={1}>
      <ChatHeader />
      <LoaderCentered />
    </View>
  );
};

const shadow = {
  ...Platform.select({
    ios: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 6,
      zIndex: 10,
    },
    android: {
      elevation: 6,
      zIndex: 10,
    },
  }),
};
