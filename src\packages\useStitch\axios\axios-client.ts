import { ENV } from '@config/env';
import axios from 'axios';
import { Platform } from 'react-native';

export let BASE_URL = ENV.BACKEND_URL?.toString() ?? 'http://localhost:3000/api';
if (Platform.OS == 'android') {
  // Remove this on release
  BASE_URL = 'http://********:3000/api';
}

export const apiClient = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});
