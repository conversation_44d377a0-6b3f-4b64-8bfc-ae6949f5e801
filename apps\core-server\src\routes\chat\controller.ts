import { ErrorResponse } from "@/lib/error-handlers-module/index.js";
import { Paginator } from "@/lib/pagination-module.js";
import { UTILS } from "@/lib/utils.js";
import db from "@repo/db";
import { chatRequestsTable, userDetails, users } from "@repo/db/schema";
import { Infer } from "@vinejs/vine/types";
import { eq, not, sql } from "drizzle-orm";
import { NextFunction, Response, Request } from "express";

export class ChatController {
  static async getBestStichers(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const stichers = await db
        .select({
          id: users.id,
          username: userDetails.userName,
          totalHealCount: userDetails.followersCount,
          avatar: userDetails.avatarUrl,
        })
        .from(users)
        .innerJoin(userDetails, eq(userDetails.userId, users.id))
        .where(not(eq(users.id, req.user.id)))
        .orderBy(sql`RANDOM()`)
        .limit(10);

      res.status(200).json({
        message: "Best stichers list",
        data: {
          data: stichers,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  static async getConversationsList(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const { limit, offset, page } = Paginator.getPage(req.query);
      const items = [{}];

      const data = Paginator.paginate(req.query, items, 10);
    } catch (error) {
      next(error);
    }
  }

  static async getConversationId(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const { userId } = req.query;
      if (!userId) {
        throw next(new ErrorResponse("Please enter a valid user id", 404));
      }
      if (req.user.id == userId) {
        throw next(
          new ErrorResponse(
            "You cannot start conversation with yourself.",
            400,
          ),
        );
      }
      const roomId = UTILS.getRoomId(req.user.id, userId as string);

      res.status(200).json({
        data: {
          data: {
            roomId,
          },
        },
      });

      return;
    } catch (error) {
      next(error);
    }
  }


}

const mockChatItem = {
  id: "1029snas0120-n1a01-2w112a12s1",
  avatar: `${[process.env.SERVER_URL]}/files/avatar1.png`,
  username: "@saintByHeart",
  lastMessage: "",
  unreadMsgCount: 0,
  isPinned: false,
};

// Requests table - contains whom requested to whom -> if one requested then other user can see accept request
// user1 request for chat -> sends a request and notify user 2 -> user 2 accepts -> calls the getConversationId then it gets the conversation item with roomId

// roomId = (user1,user2).sort(a,b=>a,b) thne lower have smaller and higher have rgitside
// eg user 1 id is abc , user 2 id is bcd then roomId will be abc_bcd and id will be dynamic with index on roomId
