import vine from "@vinejs/vine";

export const createCommentValidator = vine.object({
  text: vine.string(),
  feedItemId: vine.string(),
  replyTo: vine.string().optional(),
  threadId: vine.string().optional(),
});

export const editCommentValidator = vine.object({
  text: vine.string(),
});

export const getCommentsValidator = vine.object({
  feedItemId: vine.string(),
  threadId: vine.string().optional(),
  page: vine.string().optional(),
  limit: vine.string().optional(),
});
