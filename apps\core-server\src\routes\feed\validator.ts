import vine from "@vinejs/vine";

export const createPostValidator = vine.object({
  title: vine.string(),
  body: vine.string(),
  disableComments: vine.boolean().optional(),
});

export const createPollValidator = vine.object({
  title: vine.string(),
  options: vine.array(vine.string()),
  disableComments: vine.boolean().optional(),
});

export const votePollValidator = vine.object({
  optionId: vine.string(),
});
